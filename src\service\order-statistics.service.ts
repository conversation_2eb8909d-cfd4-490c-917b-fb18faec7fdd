import { Provide, Inject } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { Op, fn, col, literal } from 'sequelize';
import {
  Order,
  Customer,
  Employee,
  OrderDetail,
  Service,
  ServiceType,
  AdditionalService,
  AdditionalServiceOrder,
  AdditionalServiceOrderDetail,
} from '../entity';
import { OrderStatus } from '../common/Constant';

@Provide()
export class OrderStatisticsService {
  @Inject()
  ctx: Context;

  /**
   * 获取订单概览统计
   */
  async getOrderOverview(startDate?: string, endDate?: string) {
    const whereCondition: any = {};

    if (startDate && endDate) {
      whereCondition.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate)],
      };
    }

    // 总订单统计
    const totalOrders = await Order.count({ where: whereCondition });

    // 今日订单统计
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const todayOrders = await Order.count({
      where: {
        ...whereCondition,
        createdAt: {
          [Op.between]: [today, tomorrow],
        },
      },
    });

    // 本月订单统计
    const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
    const monthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 1);

    const monthOrders = await Order.count({
      where: {
        ...whereCondition,
        createdAt: {
          [Op.between]: [monthStart, monthEnd],
        },
      },
    });

    // 各状态订单统计
    const statusStats = await Order.findAll({
      where: whereCondition,
      attributes: ['status', [fn('COUNT', col('id')), 'count']],
      group: ['status'],
      raw: true,
    });

    // 收入统计
    const revenueStats = await Order.findAll({
      where: {
        ...whereCondition,
        status: {
          [Op.in]: [OrderStatus.已完成, OrderStatus.已评价],
        },
      },
      attributes: [
        [fn('SUM', col('totalFee')), 'totalRevenue'],
        [fn('COUNT', col('id')), 'completedOrders'],
      ],
      raw: true,
    });

    // 今日收入
    const todayRevenue = await Order.findAll({
      where: {
        createdAt: {
          [Op.between]: [today, tomorrow],
        },
        status: {
          [Op.in]: [OrderStatus.已完成, OrderStatus.已评价],
        },
      },
      attributes: [[fn('SUM', col('totalFee')), 'todayRevenue']],
      raw: true,
    });

    // 本月收入
    const monthRevenue = await Order.findAll({
      where: {
        createdAt: {
          [Op.between]: [monthStart, monthEnd],
        },
        status: {
          [Op.in]: [OrderStatus.已完成, OrderStatus.已评价],
        },
      },
      attributes: [[fn('SUM', col('totalFee')), 'monthRevenue']],
      raw: true,
    });

    return {
      orderStats: {
        total: totalOrders,
        today: todayOrders,
        month: monthOrders,
      },
      statusStats: statusStats.map((item: any) => ({
        status: item.status,
        count: parseInt(item.count),
      })),
      revenueStats: {
        total: parseFloat((revenueStats[0] as any)?.totalRevenue || '0'),
        today: parseFloat((todayRevenue[0] as any)?.totalRevenue || '0'),
        month: parseFloat((monthRevenue[0] as any)?.monthRevenue || '0'),
        completedOrders: parseInt(
          (revenueStats[0] as any)?.completedOrders || '0'
        ),
      },
    };
  }

  /**
   * 获取订单趋势统计
   */
  async getOrderTrend(
    startDate: string,
    endDate: string,
    groupBy: 'day' | 'week' | 'month' = 'day'
  ) {
    let groupFormat: string;

    switch (groupBy) {
      case 'week':
        groupFormat = 'YEARWEEK(createdAt)';
        break;
      case 'month':
        groupFormat = 'DATE_FORMAT(createdAt, "%Y-%m")';
        break;
      default:
        groupFormat = 'DATE(createdAt)';
    }

    const trendData = await Order.findAll({
      where: {
        createdAt: {
          [Op.between]: [new Date(startDate), new Date(endDate)],
        },
      },
      attributes: [
        [literal(groupFormat), 'period'],
        [fn('COUNT', col('id')), 'orderCount'],
        [fn('SUM', col('totalFee')), 'totalAmount'],
        [fn('AVG', col('totalFee')), 'avgAmount'],
      ],
      group: [literal(groupFormat) as any],
      order: [[literal(groupFormat) as any, 'ASC']],
      raw: true,
    });

    return trendData.map((item: any) => ({
      period: item.period,
      orderCount: parseInt(item.orderCount),
      totalAmount: parseFloat(item.totalAmount || '0'),
      avgAmount: parseFloat(item.avgAmount || '0'),
    }));
  }

  /**
   * 获取订单状态分布统计
   */
  async getStatusDistribution(startDate?: string, endDate?: string) {
    const whereCondition: any = {};

    if (startDate && endDate) {
      whereCondition.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate)],
      };
    }

    const statusData = await Order.findAll({
      where: whereCondition,
      attributes: [
        'status',
        [fn('COUNT', col('id')), 'count'],
        [fn('SUM', col('totalFee')), 'totalAmount'],
      ],
      group: ['status'],
      raw: true,
    });

    const total = statusData.reduce(
      (sum, item: any) => sum + parseInt(item.count),
      0
    );

    return statusData.map((item: any) => ({
      status: item.status,
      count: parseInt(item.count),
      percentage:
        total > 0 ? ((parseInt(item.count) / total) * 100).toFixed(2) : '0.00',
      totalAmount: parseFloat(item.totalAmount || '0'),
    }));
  }

  /**
   * 获取员工订单统计
   */
  async getEmployeeOrderStatistics(
    startDate?: string,
    endDate?: string,
    employeeId?: number,
    sortBy: 'orderCount' | 'totalAmount' | 'rating' = 'orderCount',
    sortOrder: 'asc' | 'desc' = 'desc',
    page = 1,
    pageSize = 20
  ) {
    const whereCondition: any = {};

    if (startDate && endDate) {
      whereCondition.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate)],
      };
    }

    if (employeeId) {
      whereCondition.employeeId = employeeId;
    }

    // 只统计已完成的订单
    whereCondition.status = {
      [Op.in]: [OrderStatus.已完成, OrderStatus.已评价],
    };

    const employeeStats = await Order.findAll({
      where: whereCondition,
      attributes: [
        'employeeId',
        [fn('COUNT', col('Order.id')), 'orderCount'],
        [fn('SUM', col('totalFee')), 'totalAmount'],
        [fn('AVG', col('totalFee')), 'avgAmount'],
      ],
      include: [
        {
          model: Employee,
          attributes: ['id', 'name', 'phone', 'avatar', 'rating'],
          required: true,
        },
      ],
      group: ['employeeId', 'employee.id'],
      order: [[fn('COUNT', col('Order.id')), sortOrder.toUpperCase()]],
      offset: (page - 1) * pageSize,
      limit: pageSize,
      subQuery: false,
    });

    // 获取总数
    const totalCount = await Order.findAll({
      where: whereCondition,
      attributes: ['employeeId', [fn('COUNT', col('id')), 'count']],
      group: ['employeeId'],
      raw: true,
    });

    return {
      list: employeeStats.map((item: any) => ({
        employeeId: item.employeeId,
        employeeName: item.employee?.name,
        employeePhone: item.employee?.phone,
        employeeAvatar: item.employee?.avatar,
        employeeRating: item.employee?.rating,
        orderCount: parseInt(item.get('orderCount')),
        totalAmount: parseFloat(item.get('totalAmount') || '0'),
        avgAmount: parseFloat(item.get('avgAmount') || '0'),
      })),
      total: totalCount.length,
      page,
      pageSize,
    };
  }

  /**
   * 获取客户订单统计
   */
  async getCustomerOrderStatistics(
    startDate?: string,
    endDate?: string,
    customerId?: number,
    sortBy: 'orderCount' | 'totalAmount' | 'avgAmount' = 'totalAmount',
    sortOrder: 'asc' | 'desc' = 'desc',
    page = 1,
    pageSize = 20
  ) {
    const whereCondition: any = {};

    if (startDate && endDate) {
      whereCondition.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate)],
      };
    }

    if (customerId) {
      whereCondition.customerId = customerId;
    }

    // 统计所有已支付的订单
    whereCondition.status = {
      [Op.notIn]: [OrderStatus.待付款, OrderStatus.已取消],
    };

    let orderBy: any;
    switch (sortBy) {
      case 'totalAmount':
        orderBy = [fn('SUM', col('totalFee')), sortOrder.toUpperCase()];
        break;
      case 'avgAmount':
        orderBy = [fn('AVG', col('totalFee')), sortOrder.toUpperCase()];
        break;
      default:
        orderBy = [fn('COUNT', col('Order.id')), sortOrder.toUpperCase()];
    }

    const customerStats = await Order.findAll({
      where: whereCondition,
      attributes: [
        'customerId',
        [fn('COUNT', col('Order.id')), 'orderCount'],
        [fn('SUM', col('totalFee')), 'totalAmount'],
        [fn('AVG', col('totalFee')), 'avgAmount'],
      ],
      include: [
        {
          model: Customer,
          attributes: ['id', 'nickname', 'phone', 'avatar', 'memberStatus'],
          required: true,
        },
      ],
      group: ['customerId', 'customer.id'],
      order: [orderBy],
      offset: (page - 1) * pageSize,
      limit: pageSize,
      subQuery: false,
    });

    // 获取总数
    const totalCount = await Order.findAll({
      where: whereCondition,
      attributes: ['customerId', [fn('COUNT', col('id')), 'count']],
      group: ['customerId'],
      raw: true,
    });

    return {
      list: customerStats.map((item: any) => ({
        customerId: item.customerId,
        customerName: item.customer?.nickname,
        customerPhone: item.customer?.phone,
        customerAvatar: item.customer?.avatar,
        memberStatus: item.customer?.memberStatus,
        orderCount: parseInt(item.get('orderCount')),
        totalAmount: parseFloat(item.get('totalAmount') || '0'),
        avgAmount: parseFloat(item.get('avgAmount') || '0'),
      })),
      total: totalCount.length,
      page,
      pageSize,
    };
  }

  /**
   * 获取服务类型订单统计
   */
  async getServiceTypeStatistics(startDate?: string, endDate?: string) {
    const whereCondition: any = {};

    if (startDate && endDate) {
      whereCondition.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate)],
      };
    }

    const serviceTypeStats = await OrderDetail.findAll({
      attributes: [
        [fn('COUNT', col('OrderDetail.id')), 'orderCount'],
        [fn('SUM', col('order.totalFee')), 'totalAmount'],
      ],
      include: [
        {
          model: Order,
          where: whereCondition,
          attributes: [],
          required: true,
        },
        {
          model: Service,
          attributes: ['id', 'serviceName'],
          include: [
            {
              model: ServiceType,
              attributes: ['id', 'name', 'type'],
            },
          ],
          required: true,
        },
      ],
      group: [
        'service.serviceType.id',
        'service.id',
        'service.serviceName',
        'service.serviceType.name',
        'service.serviceType.type',
      ],
      order: [[fn('COUNT', col('OrderDetail.id')), 'DESC']],
      raw: false,
    });

    return serviceTypeStats.map((item: any) => ({
      serviceTypeId: item.service?.serviceType?.id,
      serviceTypeName: item.service?.serviceType?.name,
      serviceTypeCode: item.service?.serviceType?.type,
      orderCount: parseInt(item.get('orderCount')),
      totalAmount: parseFloat(item.get('totalAmount') || '0'),
    }));
  }

  /**
   * 获取地区订单统计
   */
  async getRegionStatistics(startDate?: string, endDate?: string) {
    const whereCondition: any = {};

    if (startDate && endDate) {
      whereCondition.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate)],
      };
    }

    // 按地址统计订单分布
    const regionStats = await Order.findAll({
      where: whereCondition,
      attributes: [
        'address',
        [fn('COUNT', col('id')), 'orderCount'],
        [fn('SUM', col('totalFee')), 'totalAmount'],
        [fn('AVG', col('totalFee')), 'avgAmount'],
      ],
      group: ['address'],
      order: [[fn('COUNT', col('id')), 'DESC']],
      limit: 50, // 限制返回前50个地区
      raw: true,
    });

    return regionStats.map((item: any) => ({
      region: item.address,
      orderCount: parseInt(item.orderCount),
      totalAmount: parseFloat(item.totalAmount || '0'),
      avgAmount: parseFloat(item.avgAmount || '0'),
    }));
  }

  /**
   * 获取时段订单统计
   */
  async getTimePeriodStatistics(
    startDate?: string,
    endDate?: string,
    periodType: 'hour' | 'weekday' = 'hour'
  ) {
    const whereCondition: any = {};

    if (startDate && endDate) {
      whereCondition.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate)],
      };
    }

    let groupBy: string;
    let selectField: string;

    if (periodType === 'hour') {
      groupBy = 'HOUR(createdAt)';
      selectField = 'hour';
    } else {
      groupBy = 'WEEKDAY(createdAt)';
      selectField = 'weekday';
    }

    const timePeriodStats = await Order.findAll({
      where: whereCondition,
      attributes: [
        [literal(groupBy), selectField],
        [fn('COUNT', col('id')), 'orderCount'],
        [fn('SUM', col('totalFee')), 'totalAmount'],
        [fn('AVG', col('totalFee')), 'avgAmount'],
      ],
      group: [literal(groupBy) as any],
      order: [[literal(groupBy) as any, 'ASC']],
      raw: true,
    });

    return timePeriodStats.map((item: any) => ({
      period: item[selectField],
      periodLabel:
        periodType === 'hour'
          ? `${item[selectField]}:00-${item[selectField] + 1}:00`
          : ['周一', '周二', '周三', '周四', '周五', '周六', '周日'][
              item[selectField]
            ],
      orderCount: parseInt(item.orderCount),
      totalAmount: parseFloat(item.totalAmount || '0'),
      avgAmount: parseFloat(item.avgAmount || '0'),
    }));
  }

  /**
   * 获取订单金额分布统计
   */
  async getAmountDistribution(startDate?: string, endDate?: string) {
    const whereCondition: any = {};

    if (startDate && endDate) {
      whereCondition.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate)],
      };
    }

    // 定义金额区间
    const amountRanges = [
      { min: 0, max: 50, label: '0-50元' },
      { min: 50, max: 100, label: '50-100元' },
      { min: 100, max: 200, label: '100-200元' },
      { min: 200, max: 500, label: '200-500元' },
      { min: 500, max: 1000, label: '500-1000元' },
      { min: 1000, max: 999999, label: '1000元以上' },
    ];

    const distributionStats = await Promise.all(
      amountRanges.map(async range => {
        const count = await Order.count({
          where: {
            ...whereCondition,
            totalFee: {
              [Op.gte]: range.min,
              [Op.lt]: range.max,
            },
          },
        });

        return {
          range: range.label,
          min: range.min,
          max: range.max,
          count,
        };
      })
    );

    const total = distributionStats.reduce((sum, item) => sum + item.count, 0);

    return distributionStats.map(item => ({
      ...item,
      percentage: total > 0 ? ((item.count / total) * 100).toFixed(2) : '0.00',
    }));
  }

  /**
   * 获取用户有效订单原价统计
   * 只统计已完成和已评价的订单
   */
  async getCustomerOriginalPriceStatistics(
    customerId: number,
    startDate?: string,
    endDate?: string
  ) {
    const whereCondition: any = {
      customerId,
      status: {
        [Op.in]: [OrderStatus.已完成, OrderStatus.已评价],
      },
    };

    if (startDate && endDate) {
      whereCondition.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate)],
      };
    }

    // 主订单统计
    const orderStats = await Order.findAll({
      where: whereCondition,
      attributes: [
        [fn('COUNT', col('id')), 'orderCount'],
        [fn('SUM', col('originalPrice')), 'totalOriginalPrice'],
        [fn('SUM', col('totalFee')), 'totalPaidAmount'],
        [fn('SUM', col('cardDeduction')), 'totalCardDeduction'],
        [fn('SUM', col('couponDeduction')), 'totalCouponDeduction'],
        [
          fn('SUM', col('additionalServiceOriginalPrice')),
          'totalAdditionalOriginalPrice',
        ],
        [
          fn('SUM', col('additionalServiceAmount')),
          'totalAdditionalPaidAmount',
        ],
      ],
      raw: true,
    });

    const stats = orderStats[0] as any;

    return {
      customerId,
      orderCount: parseInt(stats.orderCount || '0'),
      // 主订单原价统计
      mainOrderOriginalPrice: parseFloat(stats.totalOriginalPrice || '0'),
      mainOrderPaidAmount: parseFloat(stats.totalPaidAmount || '0'),
      // 追加服务原价统计
      additionalServiceOriginalPrice: parseFloat(
        stats.totalAdditionalOriginalPrice || '0'
      ),
      additionalServicePaidAmount: parseFloat(
        stats.totalAdditionalPaidAmount || '0'
      ),
      // 总计
      totalOriginalPrice:
        parseFloat(stats.totalOriginalPrice || '0') +
        parseFloat(stats.totalAdditionalOriginalPrice || '0'),
      totalPaidAmount:
        parseFloat(stats.totalPaidAmount || '0') +
        parseFloat(stats.totalAdditionalPaidAmount || '0'),
      // 优惠统计
      totalCardDeduction: parseFloat(stats.totalCardDeduction || '0'),
      totalCouponDeduction: parseFloat(stats.totalCouponDeduction || '0'),
      totalDeduction:
        parseFloat(stats.totalCardDeduction || '0') +
        parseFloat(stats.totalCouponDeduction || '0'),
    };
  }

  /**
   * 获取用户有效订单列表
   * 只查询已完成和已评价的订单，包含详细的服务和增项服务信息
   */
  async getCustomerValidOrderList(
    customerId: number,
    startDate?: string,
    endDate?: string,
    sortBy:
      | 'orderTime'
      | 'serviceTime'
      | 'originalPrice'
      | 'totalFee' = 'orderTime',
    sortOrder: 'asc' | 'desc' = 'desc',
    page = 1,
    pageSize = 20
  ) {
    const whereCondition: any = {
      customerId,
      status: {
        [Op.in]: [OrderStatus.已完成, OrderStatus.已评价],
      },
    };

    if (startDate && endDate) {
      whereCondition.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate)],
      };
    }

    // 构建排序条件
    let orderBy: any;
    switch (sortBy) {
      case 'serviceTime':
        orderBy = ['serviceTime', sortOrder.toUpperCase()];
        break;
      case 'originalPrice':
        orderBy = ['originalPrice', sortOrder.toUpperCase()];
        break;
      case 'totalFee':
        orderBy = ['totalFee', sortOrder.toUpperCase()];
        break;
      default:
        orderBy = ['orderTime', sortOrder.toUpperCase()];
    }

    const orders = await Order.findAndCountAll({
      where: whereCondition,
      include: [
        {
          model: OrderDetail,
          include: [
            {
              model: Service,
              include: [
                {
                  model: ServiceType,
                  attributes: ['id', 'name', 'type'],
                },
              ],
              attributes: ['id', 'serviceName', 'basePrice', 'serviceTypeId'],
            },
            {
              model: AdditionalService,
              attributes: ['id', 'name', 'type', 'price'],
              through: { attributes: [] },
            },
            {
              model: AdditionalServiceOrder,
              include: [
                {
                  model: AdditionalServiceOrderDetail,
                  as: 'details',
                  // 暂时移除错误的 Service 关联，等重启项目后使用正确的 AdditionalService 关联
                  // 使用冗余字段 serviceName 和 servicePrice
                  attributes: [
                    'id',
                    'serviceId',
                    'serviceName',
                    'servicePrice',
                    'quantity',
                  ],
                },
              ],
              attributes: [
                'id',
                'sn',
                'status',
                'originalPrice',
                'totalFee',
                'cardDeduction',
                'couponDeduction',
                'createdAt',
              ],
            },
          ],
          attributes: [
            'id',
            'serviceId',
            'serviceName',
            'servicePrice',
            'petName',
            'petType',
            'petBreed',
            'userRemark',
          ],
        },
      ],
      order: [orderBy],
      offset: (page - 1) * pageSize,
      limit: pageSize,
      attributes: [
        'id',
        'sn',
        'status',
        'orderTime',
        'serviceTime',
        'originalPrice',
        'totalFee',
        'cardDeduction',
        'couponDeduction',
        'additionalServiceOriginalPrice',
        'additionalServiceAmount',
        'address',
        'addressDetail',
      ],
    });

    return {
      list: orders.rows.map((order: any) => {
        // 计算主订单服务信息
        const mainServices = (order.orderDetails || []).map((detail: any) => ({
          id: detail.id,
          serviceId: detail.serviceId,
          serviceName: detail.serviceName,
          servicePrice: parseFloat(detail.servicePrice || '0'),
          serviceType: detail.service?.serviceType?.name || '',
          serviceTypeName: detail.service?.serviceType?.type || '',
          petName: detail.petName,
          petType: detail.petType,
          petBreed: detail.petBreed,
          userRemark: detail.userRemark,
          // 订单详情关联的增项服务
          additionalServices: (detail.additionalServices || []).map(
            (addService: any) => ({
              id: addService.id,
              name: addService.name,
              type: addService.type,
              price: parseFloat(addService.price || '0'),
            })
          ),
        }));

        // 计算追加服务信息
        const additionalServiceOrders = (order.orderDetails || []).flatMap(
          (detail: any) =>
            (detail.additionalServiceOrders || []).map((addOrder: any) => ({
              id: addOrder.id,
              sn: addOrder.sn,
              status: addOrder.status,
              originalPrice: parseFloat(addOrder.originalPrice || '0'),
              totalFee: parseFloat(addOrder.totalFee || '0'),
              cardDeduction: parseFloat(addOrder.cardDeduction || '0'),
              couponDeduction: parseFloat(addOrder.couponDeduction || '0'),
              createdAt: addOrder.createdAt,
              services: (addOrder.details || []).map((orderDetail: any) => ({
                id: orderDetail.id,
                serviceId: orderDetail.serviceId,
                serviceName: orderDetail.serviceName,
                servicePrice: parseFloat(orderDetail.servicePrice || '0'),
                quantity: orderDetail.quantity,
                serviceType: orderDetail.service?.serviceType?.name || '',
                serviceTypeName: orderDetail.service?.serviceType?.type || '',
              })),
            }))
        );

        return {
          id: order.id,
          sn: order.sn,
          status: order.status,
          orderTime: order.orderTime,
          serviceTime: order.serviceTime,
          address: order.address,
          addressDetail: order.addressDetail,
          // 价格信息
          originalPrice: parseFloat(order.originalPrice || '0'),
          totalFee: parseFloat(order.totalFee || '0'),
          cardDeduction: parseFloat(order.cardDeduction || '0'),
          couponDeduction: parseFloat(order.couponDeduction || '0'),
          additionalServiceOriginalPrice: parseFloat(
            order.additionalServiceOriginalPrice || '0'
          ),
          additionalServiceAmount: parseFloat(
            order.additionalServiceAmount || '0'
          ),
          // 总计价格
          totalOriginalPrice:
            parseFloat(order.originalPrice || '0') +
            parseFloat(order.additionalServiceOriginalPrice || '0'),
          totalPaidAmount:
            parseFloat(order.totalFee || '0') +
            parseFloat(order.additionalServiceAmount || '0'),
          // 服务信息
          mainServices,
          additionalServiceOrders,
        };
      }),
      total: orders.count,
      page,
      pageSize,
    };
  }
}
