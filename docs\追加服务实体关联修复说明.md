# 追加服务实体关联修复说明

## 修复概述

本次修复解决了 `AdditionalServiceOrderDetail` 实体中外键关联错误的问题，将外键从错误的 `Service` 表改为正确的 `AdditionalService` 表。

## 问题背景

### 原始问题
- `AdditionalServiceOrderDetail.serviceId` 字段实际存储的是 `AdditionalService` 的 ID
- 但实体定义中外键错误地关联到了 `Service` 表
- 导致查询时出现 `Service is not associated to AdditionalServiceOrderDetail` 错误
- 无法正确获取增项服务信息，影响接口功能

### 业务影响
- 追加服务相关接口无法正常工作
- 员工端无法查看待确认的追加服务
- 管理端无法正确显示追加服务信息
- 订单统计数据不准确

## 修复内容

### 1. 实体定义修正

#### AdditionalServiceOrderDetail 实体 (`src/entity/additional-service-order-detail.entity.ts`)
```typescript
// 修复前（错误）
@ForeignKey(() => Service)
serviceId: number;

@BelongsTo(() => Service)
service?: Service;

// 修复后（正确）
@ForeignKey(() => AdditionalService)
serviceId: number;

@BelongsTo(() => AdditionalService)
additionalService?: AdditionalService;
```

#### AdditionalService 实体 (`src/entity/additional-service.entity.ts`)
```typescript
// 新增反向关联
@HasMany(() => require('./additional-service-order-detail.entity').AdditionalServiceOrderDetail)
additionalServiceOrderDetails: any[];
```

### 2. 查询逻辑修正

修复了以下文件中的错误关联查询：

1. **src/controller/additional-service-order.controller.ts**
   - 修复 3 处错误的 `include: [Service]`
   - 恢复正确的 `AdditionalService` 关联查询

2. **src/controller/order.controller.ts**
   - 修复订单查询中的追加服务关联

3. **src/controller/admin/order-admin.controller.ts**
   - 修复管理端订单查询中的追加服务关联

4. **src/controller/admin/additional-service-admin.controller.ts**
   - 修复管理端追加服务查询

5. **src/service/order-statistics.service.ts**
   - 修复订单统计中的追加服务关联

### 3. 数据库迁移

创建了数据库迁移文件 `src/migration/20250713-fix-additional-service-order-detail-foreign-key.sql`：

```sql
-- 删除原有的错误外键约束
ALTER TABLE additional_service_order_details 
DROP FOREIGN KEY additional_service_order_details_ibfk_2;

-- 添加正确的外键约束到 additional_services 表
ALTER TABLE additional_service_order_details 
ADD CONSTRAINT fk_additional_service_order_details_service_id 
FOREIGN KEY (serviceId) REFERENCES additional_services(id) 
ON DELETE NO ACTION ON UPDATE CASCADE;
```

## 修复后的优势

### 1. 数据一致性
- 实体关联与实际业务逻辑保持一致
- 外键约束正确指向 `additional_services` 表

### 2. 查询性能
- 可以使用 Sequelize 关联查询，避免手动查询
- 减少数据库查询次数，提高性能

### 3. 代码维护性
- 类型安全，TypeScript 类型定义正确
- 代码结构更清晰，易于维护

### 4. 向后兼容
- 返回的数据结构保持不变
- 前端无需修改

## 受影响的接口

### 员工端接口
- `GET /employee/additional-services/pending` - 查询待确认的追加服务
- `GET /order-details/{orderDetailId}/additional-services` - 查询追加服务列表

### 管理端接口
- `GET /admin/orders/{orderId}/additional-services` - 查询订单追加服务
- `GET /admin/additional-services` - 追加服务管理

### 用户端接口
- `GET /order-details/{orderDetailId}/additional-services` - 查询追加服务列表

## 验证方法

### 1. 接口测试
```bash
# 测试员工端待确认追加服务接口
GET /employee/additional-services/pending?employeeId=7&current=1&pageSize=10

# 测试追加服务列表接口
GET /order-details/123/additional-services
```

### 2. 数据验证
- 确认返回数据中包含正确的增项服务信息
- 验证 `needDurationTracking` 等字段能正确获取
- 检查数据结构是否符合文档说明

### 3. 性能验证
- 查询时间应该有所改善（使用关联查询替代手动查询）
- 数据库查询次数减少

## 注意事项

1. **数据库迁移**: 需要执行 SQL 迁移文件更新外键约束
2. **项目重启**: 修改实体关联后需要重启项目让 Sequelize 重新加载
3. **测试验证**: 建议在测试环境充分验证后再部署到生产环境

## 相关文档

- [订单接口文档](./api/订单接口.md) - 已更新追加服务接口说明
- [数据库迁移文件](../src/migration/20250713-fix-additional-service-order-detail-foreign-key.sql)
