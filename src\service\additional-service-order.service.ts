import { Provide, Inject } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { ILogger } from '@midwayjs/logger';
import { BaseService } from '../common/BaseService';
import { CustomError } from '../error/custom.error';
import {
  AdditionalServiceOrder,
  AdditionalServiceOrderStatus,
} from '../entity/additional-service-order.entity';
import { AdditionalServiceOrderDetail } from '../entity/additional-service-order-detail.entity';
import { AdditionalServiceDiscountInfo } from '../entity/additional-service-discount-info.entity';
import { OrderDetail } from '../entity/order-detail.entity';
import { Order } from '../entity/order.entity';
import { Customer } from '../entity/customer.entity';
import { Employee } from '../entity/employee.entity';
import { OrderStatus } from '../common/Constant';
import { DiscountType } from '../entity/order-discount-info.entity';
import { AdditionalServiceDiscountInfoService } from './additional-service-discount-info.service';
import { WepayService } from './wepay.service';
import { AdditionalService } from '../entity';

/**
 * 创建追加服务订单数据接口
 */
export interface CreateAdditionalServiceOrderData {
  /** 订单详情ID */
  orderDetailId: number;
  /** 客户ID */
  customerId: number;
  /** 服务明细列表 */
  services: {
    serviceId: number;
    quantity: number;
  }[];
  /** 优惠信息列表 */
  discountInfos?: {
    discountType: DiscountType;
    discountId: number;
    discountAmount: number;
  }[];
  /** 备注 */
  remark?: string;
}

@Provide()
export class AdditionalServiceOrderService extends BaseService<AdditionalServiceOrder> {
  @Inject()
  ctx: Context;

  @Inject()
  logger: ILogger;

  @Inject()
  additionalServiceDiscountInfoService: AdditionalServiceDiscountInfoService;

  @Inject()
  wepayService: WepayService;

  constructor() {
    super('追加服务订单');
  }

  getModel = () => {
    return AdditionalServiceOrder;
  };

  /**
   * 创建追加服务订单
   */
  async createAdditionalServiceOrder(data: CreateAdditionalServiceOrderData) {
    const { orderDetailId, customerId, services, discountInfos, remark } = data;

    // 验证订单详情是否存在且状态为服务中
    const orderDetail = await OrderDetail.findByPk(orderDetailId, {
      include: [
        {
          model: Order,
          include: [Customer, Employee],
        },
      ],
    });

    if (!orderDetail) {
      throw new CustomError('订单详情不存在');
    }

    if (orderDetail.order.status !== OrderStatus.服务中) {
      throw new CustomError('只有服务中的订单才能申请追加服务');
    }

    if (orderDetail.order.customerId !== customerId) {
      throw new CustomError('无权限操作此订单');
    }

    // 验证服务是否存在并计算价格
    let originalPrice = 0;
    const serviceDetails = [];

    for (const serviceItem of services) {
      const service = await AdditionalService.findByPk(serviceItem.serviceId);
      if (!service) {
        throw new CustomError(`服务ID ${serviceItem.serviceId} 不存在`);
      }
      // if (!service.published) {
      //   throw new CustomError(`服务 ${service.serviceName} 已下架`);
      // }

      const itemPrice = Number(service.price) * serviceItem.quantity;
      originalPrice += itemPrice;

      serviceDetails.push({
        serviceId: service.id,
        serviceName: service.name,
        servicePrice: service.price,
        quantity: serviceItem.quantity,
      });
    }

    // 计算优惠金额
    let cardDeduction = 0;
    let couponDeduction = 0;

    if (discountInfos && discountInfos.length > 0) {
      for (const discount of discountInfos) {
        if (discount.discountType === DiscountType.MEMBERSHIP_CARD) {
          cardDeduction += Number(discount.discountAmount);
        } else if (discount.discountType === DiscountType.COUPON) {
          couponDeduction += Number(discount.discountAmount);
        }
      }
    }

    const totalFee = originalPrice - cardDeduction - couponDeduction;

    if (totalFee < 0) {
      throw new CustomError('优惠金额不能超过订单总价');
    }

    // 生成追加服务订单编号
    const timestamp = Date.now().toString();
    const random = Math.floor(Math.random() * 10000)
      .toString()
      .padStart(4, '0');
    const sn = `ADD${timestamp}${random}`;

    // 使用事务创建追加服务订单
    const result = await AdditionalServiceOrder.sequelize.transaction(
      async t => {
        // 创建追加服务订单
        const additionalServiceOrder = await AdditionalServiceOrder.create(
          {
            sn,
            orderDetailId,
            customerId,
            employeeId: orderDetail.order.employeeId,
            status: AdditionalServiceOrderStatus.PENDING_CONFIRM,
            originalPrice,
            totalFee,
            cardDeduction,
            couponDeduction,
            remark,
          },
          { transaction: t }
        );

        // 创建追加服务明细
        await Promise.all(
          serviceDetails.map(detail =>
            AdditionalServiceOrderDetail.create(
              {
                additionalServiceOrderId: additionalServiceOrder.id,
                ...detail,
              },
              { transaction: t }
            )
          )
        );

        return additionalServiceOrder;
      }
    );

    // 在事务外处理优惠券逻辑
    if (discountInfos && discountInfos.length > 0) {
      try {
        await this.additionalServiceDiscountInfoService.processAdditionalServiceDiscounts(
          result.id,
          discountInfos
        );
      } catch (error) {
        // 如果优惠券处理失败，需要回滚已创建的追加服务订单
        await result.destroy();
        throw error;
      }
    }

    // 更新主订单的追加服务信息
    await this.updateMainOrderAdditionalServiceInfo(result.orderDetailId);

    return result;
  }

  /**
   * 员工确认追加服务
   */
  async confirmAdditionalService(id: number, employeeId: number) {
    const additionalServiceOrder = await AdditionalServiceOrder.findByPk(id, {
      include: [
        {
          model: OrderDetail,
          include: [Order],
        },
      ],
    });

    if (!additionalServiceOrder) {
      throw new CustomError('追加服务订单不存在');
    }

    if (
      additionalServiceOrder.status !==
      AdditionalServiceOrderStatus.PENDING_CONFIRM
    ) {
      throw new CustomError('当前状态不允许确认操作');
    }

    if (additionalServiceOrder.orderDetail.order.employeeId !== employeeId) {
      throw new CustomError('无权限操作此追加服务');
    }

    // 检查订单金额，如果为0元则直接设为已支付状态
    // 注意：totalFee 可能是字符串类型（如 "0.00"），需要转换为数字进行比较
    const totalFeeNumber = Number(additionalServiceOrder.totalFee);
    const isZeroAmount = totalFeeNumber === 0;
    const updateData: any = {
      confirmTime: new Date(),
    };

    this.logger.info(
      `【确认追加服务订单】：订单ID=${id}, 原始金额=${
        additionalServiceOrder.totalFee
      }, 类型=${typeof additionalServiceOrder.totalFee}, 转换后金额=${totalFeeNumber}, 是否0元=${isZeroAmount}`
    );

    if (isZeroAmount) {
      // 0元订单直接设为已支付
      updateData.status = AdditionalServiceOrderStatus.PAID;
      updateData.payTime = new Date();

      this.logger.info(
        `【0元追加服务订单自动支付】：订单ID=${id}, 设置状态为=${updateData.status}`
      );
    } else {
      // 非0元订单设为已确认，等待支付
      updateData.status = AdditionalServiceOrderStatus.CONFIRMED;
      this.logger.info(
        `【非0元追加服务订单确认】：订单ID=${id}, 设置状态为=${updateData.status}`
      );
    }

    await additionalServiceOrder.update(updateData);

    // 查询更新后的状态
    const updatedOrder = await AdditionalServiceOrder.findByPk(id, {
      attributes: ['id', 'status', 'totalFee', 'confirmTime', 'payTime'],
    });
    console.log('【更新后的订单状态】：', updatedOrder);

    // 更新主订单的追加服务信息
    await this.updateMainOrderAdditionalServiceInfo(
      additionalServiceOrder.orderDetailId
    );

    return {
      success: true,
      isZeroAmount,
      status: updateData.status,
      message: isZeroAmount
        ? '0元订单已自动支付完成'
        : '追加服务已确认，等待用户支付',
    };
  }

  /**
   * 员工拒绝追加服务
   */
  async rejectAdditionalService(
    id: number,
    employeeId: number,
    rejectReason: string
  ) {
    const additionalServiceOrder = await AdditionalServiceOrder.findByPk(id, {
      include: [
        {
          model: OrderDetail,
          include: [Order],
        },
      ],
    });

    if (!additionalServiceOrder) {
      throw new CustomError('追加服务订单不存在');
    }

    if (
      additionalServiceOrder.status !==
      AdditionalServiceOrderStatus.PENDING_CONFIRM
    ) {
      throw new CustomError('当前状态不允许拒绝操作');
    }

    if (additionalServiceOrder.orderDetail.order.employeeId !== employeeId) {
      throw new CustomError('无权限操作此追加服务');
    }

    await additionalServiceOrder.update({
      status: AdditionalServiceOrderStatus.REJECTED,
      rejectReason,
    });

    // 更新主订单的追加服务信息
    await this.updateMainOrderAdditionalServiceInfo(
      additionalServiceOrder.orderDetailId
    );

    return true;
  }

  /**
   * 支付追加服务订单
   */
  async payAdditionalServiceOrder(id: number, customerId: number) {
    const additionalServiceOrder = await AdditionalServiceOrder.findByPk(id, {
      include: [Customer],
    });

    if (!additionalServiceOrder) {
      throw new CustomError('追加服务订单不存在');
    }

    if (additionalServiceOrder.customerId !== customerId) {
      throw new CustomError('无权限操作此追加服务订单');
    }

    if (
      additionalServiceOrder.status !== AdditionalServiceOrderStatus.CONFIRMED
    ) {
      throw new CustomError('追加服务未确认，无法支付');
    }

    // 如果是0元订单，直接更新状态
    // 注意：totalFee 可能是字符串类型（如 "0.00"），需要转换为数字进行比较
    if (Number(additionalServiceOrder.totalFee) === 0) {
      await additionalServiceOrder.update({
        status: AdditionalServiceOrderStatus.PAID,
        payTime: new Date(),
      });

      // 更新主订单的追加服务信息
      await this.updateMainOrderAdditionalServiceInfo(
        additionalServiceOrder.orderDetailId
      );

      return { success: true, message: '0元订单支付成功' };
    }

    // 非0元订单返回支付信息，需要前端调用微信支付
    return {
      success: false,
      needPay: true,
      orderSn: additionalServiceOrder.sn,
      totalFee: additionalServiceOrder.totalFee,
      message: '需要调用微信支付',
    };
  }

  /**
   * 用户删除追加服务申请
   */
  async deleteAdditionalServiceOrder(id: number, customerId?: number) {
    const additionalServiceOrder = await AdditionalServiceOrder.findByPk(id);

    if (!additionalServiceOrder) {
      throw new CustomError('追加服务订单不存在');
    }

    if (customerId && additionalServiceOrder.customerId !== customerId) {
      throw new CustomError('无权限操作此追加服务订单');
    }

    if (
      [
        AdditionalServiceOrderStatus.PENDING_CONFIRM,
        AdditionalServiceOrderStatus.PAID,
        AdditionalServiceOrderStatus.REFUNDING,
        AdditionalServiceOrderStatus.COMPLETED,
      ].includes(additionalServiceOrder.status)
    ) {
      throw new CustomError('不允许删除此状态的追加服务申请');
    }

    // 先处理优惠券退回
    try {
      const refundStatus =
        await this.additionalServiceDiscountInfoService.checkAdditionalServiceRefundStatus(
          id
        );
      if (refundStatus.hasDiscountInfos && !refundStatus.allRefunded) {
        await this.additionalServiceDiscountInfoService.refundAdditionalServiceDiscounts(
          id,
          '用户删除追加服务申请'
        );
      }
    } catch (error) {
      this.logger.error('处理追加服务优惠券退回失败:', error);
      // 优惠券退回失败不应该阻止删除操作，但需要记录日志
    }

    // 保存orderDetailId用于后续更新主订单信息
    const orderDetailId = additionalServiceOrder.orderDetailId;

    // 使用事务删除追加服务订单及其相关数据
    await AdditionalServiceOrder.sequelize.transaction(async t => {
      // 删除优惠信息
      await AdditionalServiceDiscountInfo.destroy({
        where: { additionalServiceOrderId: id },
        transaction: t,
      });

      // 删除订单明细
      await AdditionalServiceOrderDetail.destroy({
        where: { additionalServiceOrderId: id },
        transaction: t,
      });

      // 删除追加服务订单
      await additionalServiceOrder.destroy({ transaction: t });
    });

    // 更新主订单的追加服务信息
    await this.updateMainOrderAdditionalServiceInfo(orderDetailId);

    return true;
  }

  /**
   * 退款追加服务订单（用于主订单退款时调用）
   */
  async refundAdditionalServiceOrder(
    id: number,
    reason: string,
    shouldRefundCoupons = true
  ) {
    const additionalServiceOrder = await AdditionalServiceOrder.findByPk(id);

    if (!additionalServiceOrder) {
      throw new CustomError('追加服务订单不存在');
    }

    if (
      ![
        AdditionalServiceOrderStatus.PAID,
        AdditionalServiceOrderStatus.COMPLETED,
      ].includes(additionalServiceOrder.status)
    ) {
      throw new CustomError('只能退款已支付的追加服务订单');
    }

    // 处理优惠券退回（只有在shouldRefundCoupons为true时才退回）
    if (shouldRefundCoupons) {
      try {
        const refundStatus =
          await this.additionalServiceDiscountInfoService.checkAdditionalServiceRefundStatus(
            id
          );
        if (refundStatus.hasDiscountInfos && !refundStatus.allRefunded) {
          await this.additionalServiceDiscountInfoService.refundAdditionalServiceDiscounts(
            id,
            reason
          );
        }
      } catch (error) {
        this.logger.error('处理追加服务优惠券退回失败:', error);
        throw error;
      }
    } else {
      this.logger.info(`追加服务订单${id}退款，但不退回优惠券：${reason}`);
    }

    // 调用微信退款接口
    try {
      await this.wepayService.refundAdditionalService(
        additionalServiceOrder.sn,
        Number(additionalServiceOrder.totalFee)
      );
      this.logger.info(
        `追加服务订单 ${additionalServiceOrder.sn} 微信退款成功`
      );
    } catch (error) {
      this.logger.error(
        `追加服务订单 ${additionalServiceOrder.sn} 微信退款失败:`,
        error
      );
      throw new CustomError(`微信退款失败: ${error.message}`);
    }

    // 更新主订单的追加服务信息
    await this.updateMainOrderAdditionalServiceInfo(
      additionalServiceOrder.orderDetailId
    );

    return true;
  }

  /**
   * 完成追加服务订单（主订单完成时调用）
   */
  async completeAdditionalService(id: number) {
    const additionalServiceOrder = await AdditionalServiceOrder.findByPk(id);

    if (!additionalServiceOrder) {
      throw new CustomError('追加服务订单不存在');
    }

    if (additionalServiceOrder.status !== AdditionalServiceOrderStatus.PAID) {
      throw new CustomError('只能完成已支付的追加服务订单');
    }

    await additionalServiceOrder.update({
      status: AdditionalServiceOrderStatus.COMPLETED,
    });

    // 更新主订单的追加服务信息
    await this.updateMainOrderAdditionalServiceInfo(
      additionalServiceOrder.orderDetailId
    );

    return true;
  }

  /**
   * 管理员退款追加服务订单 - 无状态检查，可以退款任何状态的追加服务订单
   */
  async adminRefundAdditionalServiceOrder(
    id: number,
    reason: string,
    shouldRefundCoupons = true
  ) {
    const additionalServiceOrder = await AdditionalServiceOrder.findByPk(id);

    if (!additionalServiceOrder) {
      throw new CustomError('追加服务订单不存在');
    }

    // 管理员退款无需状态检查，可以对任何状态的追加服务订单操作
    this.logger.info(
      `管理员退款追加服务订单：ID=${id}, 状态=${additionalServiceOrder.status}, 原因=${reason}`
    );

    // 处理优惠券退回（只有在shouldRefundCoupons为true时才退回）
    if (shouldRefundCoupons) {
      try {
        const refundStatus =
          await this.additionalServiceDiscountInfoService.checkAdditionalServiceRefundStatus(
            id
          );
        if (refundStatus.hasDiscountInfos && !refundStatus.allRefunded) {
          await this.additionalServiceDiscountInfoService.refundAdditionalServiceDiscounts(
            id,
            reason
          );
        }
      } catch (error) {
        this.logger.error('处理追加服务优惠券退回失败:', error);
        throw error;
      }
    } else {
      this.logger.info(`追加服务订单${id}退款，但不退回优惠券：${reason}`);
    }

    // 调用管理员专用的微信退款接口
    try {
      await this.wepayService.adminRefundAdditionalService(
        additionalServiceOrder.sn,
        Number(additionalServiceOrder.totalFee)
      );
      this.logger.info(
        `管理员退款追加服务订单 ${additionalServiceOrder.sn} 微信退款成功`
      );
    } catch (error) {
      this.logger.error(
        `管理员退款追加服务订单 ${additionalServiceOrder.sn} 微信退款失败:`,
        error
      );
      throw new CustomError(`微信退款失败: ${error.message}`);
    }

    // 更新主订单的追加服务信息
    await this.updateMainOrderAdditionalServiceInfo(
      additionalServiceOrder.orderDetailId
    );

    return true;
  }

  /**
   * 更新主订单的追加服务信息
   */
  async updateMainOrderAdditionalServiceInfo(orderDetailId: number) {
    const orderDetail = await OrderDetail.findByPk(orderDetailId, {
      include: [Order],
    });

    if (!orderDetail) {
      return;
    }

    // 查询该订单所有追加服务（不限状态）
    const allAdditionalServices = await AdditionalServiceOrder.findAll({
      include: [
        {
          model: OrderDetail,
          where: {
            orderId: orderDetail.orderId,
          },
        },
      ],
    });

    // 计算冗余字段
    const hasAdditionalServices = allAdditionalServices.length > 0;

    // additionalServiceAmount: 所有状态的追加服务实付总价
    const additionalServiceAmount = allAdditionalServices.reduce(
      (sum, item) => sum + Number(item.totalFee),
      0
    );

    // additionalServiceOriginalPrice: 所有状态的追加服务原总价
    const additionalServiceOriginalPrice = allAdditionalServices.reduce(
      (sum, item) => sum + Number(item.originalPrice),
      0
    );

    // 判断所有追加服务是否已完成（rejected、completed、cancelled、refunded）
    const completedStatuses = [
      AdditionalServiceOrderStatus.REJECTED,
      AdditionalServiceOrderStatus.COMPLETED,
      AdditionalServiceOrderStatus.CANCELLED,
      AdditionalServiceOrderStatus.REFUNDED,
    ];
    const additionalServicesCompleted =
      hasAdditionalServices &&
      allAdditionalServices.every(item =>
        completedStatuses.includes(item.status)
      );

    await orderDetail.order.update({
      hasAdditionalServices,
      additionalServiceAmount,
      additionalServiceOriginalPrice,
      additionalServicesCompleted,
    });
  }

  /**
   * 同步追加服务订单支付状态
   * 通过查询微信支付状态来更新本地订单状态
   */
  async syncAdditionalServicePaymentStatus(
    id: number,
    orderDetailId: number,
    operatorId?: number
  ) {
    // 查询本地追加服务订单
    const localOrder = await AdditionalServiceOrder.findOne({
      where: { id, orderDetailId },
      attributes: [
        'id',
        'sn',
        'status',
        'totalFee',
        'prepayId',
        'payTime',
        'customerId',
      ],
    });

    if (!localOrder) {
      throw new CustomError('追加服务订单不存在');
    }

    this.logger.info('【同步支付状态】开始同步追加服务订单：', {
      orderId: id,
      orderSn: localOrder.sn,
      currentStatus: localOrder.status,
      totalFee: localOrder.totalFee,
      operatorId,
    });

    // 如果订单已经是已支付状态，直接返回
    if (localOrder.status === AdditionalServiceOrderStatus.PAID) {
      return {
        success: true,
        alreadyPaid: true,
        message: '订单已经是已支付状态',
        localStatus: localOrder.status,
        payTime: localOrder.payTime,
      };
    }

    // 如果订单状态不是已确认，不能同步支付状态
    if (localOrder.status !== AdditionalServiceOrderStatus.CONFIRMED) {
      return {
        success: false,
        message: `订单状态为 ${localOrder.status}，只有已确认状态的订单才能同步支付状态`,
        localStatus: localOrder.status,
      };
    }

    // 如果是0元订单，直接更新为已支付
    if (Number(localOrder.totalFee) === 0) {
      await localOrder.update({
        status: AdditionalServiceOrderStatus.PAID,
        payTime: new Date(),
      });

      // 更新主订单的追加服务信息
      await this.updateMainOrderAdditionalServiceInfo(orderDetailId);

      this.logger.info('【同步支付状态】0元订单直接更新为已支付：', {
        orderId: id,
      });

      return {
        success: true,
        isZeroAmount: true,
        message: '0元订单已更新为已支付状态',
        localStatus: AdditionalServiceOrderStatus.PAID,
        payTime: new Date(),
      };
    }

    // 查询微信支付状态
    try {
      const wechatPaymentStatus = await this.wepayService.getTransactionsBySN(
        localOrder.sn
      );

      this.logger.info('【同步支付状态】微信支付查询结果：', {
        orderId: id,
        orderSn: localOrder.sn,
        wechatStatus: wechatPaymentStatus,
      });

      // 如果微信支付状态为成功，更新本地订单状态
      if (
        wechatPaymentStatus &&
        wechatPaymentStatus.trade_state === 'SUCCESS'
      ) {
        await localOrder.update({
          status: AdditionalServiceOrderStatus.PAID,
          payTime: new Date(),
        });

        // 更新主订单的追加服务信息
        await this.updateMainOrderAdditionalServiceInfo(orderDetailId);

        this.logger.info('【同步支付状态】根据微信支付状态更新订单为已支付：', {
          orderId: id,
          wechatTransactionId: wechatPaymentStatus.transaction_id,
        });

        return {
          success: true,
          syncFromWechat: true,
          message: '根据微信支付状态更新为已支付',
          localStatus: AdditionalServiceOrderStatus.PAID,
          wechatStatus: wechatPaymentStatus.trade_state,
          wechatTransactionId: wechatPaymentStatus.transaction_id,
          payTime: new Date(),
        };
      } else {
        // 微信支付状态不是成功，返回当前状态
        return {
          success: false,
          message: `微信支付状态为 ${
            wechatPaymentStatus?.trade_state || '未知'
          }，订单未支付成功`,
          localStatus: localOrder.status,
          wechatStatus: wechatPaymentStatus?.trade_state,
          wechatTransactionId: wechatPaymentStatus?.transaction_id,
        };
      }
    } catch (error) {
      this.logger.error('【同步支付状态】查询微信支付状态失败：', {
        orderId: id,
        orderSn: localOrder.sn,
        error: error.message,
      });

      return {
        success: false,
        message: `查询微信支付状态失败：${error.message}`,
        localStatus: localOrder.status,
        error: error.message,
      };
    }
  }
}
