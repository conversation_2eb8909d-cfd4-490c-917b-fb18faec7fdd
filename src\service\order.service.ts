import { <PERSON>ogger, Inject, Provide } from '@midwayjs/core';
import {
  AdditionalService,
  Customer,
  Employee,
  Order,
  OrderDetail,
  Service,
  ServiceChangeLog,
  ServiceType,
} from '../entity';
import { ServiceDurationRecord } from '../entity/service-duration-record.entity';
import {
  AdditionalServiceOrder,
  AdditionalServiceOrderStatus,
} from '../entity/additional-service-order.entity';
import { Op } from 'sequelize';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import * as lodash from 'lodash';
import { CustomError } from '../error/custom.error';
import {
  OrderStatus,
  OrderStatusChangeType,
  POSITION_SERVICE_MAPPING,
} from '../common/Constant';
import { WepayService } from './wepay.service';
import { OrderDiscountInfoService } from './order-discount-info.service';
import { MessageBroadcastService } from './message-broadcast.service';
import { WeappService } from './weapp.service';
import { MessageHelper } from '../utils/message.helper';
import { ServicePhotoService } from './service-photo.service';
import { AdditionalServiceOrderService } from './additional-service-order.service';
import { ServiceDurationRecordService } from './service-duration-record.service';

@Provide()
export class OrderService extends BaseService<Order> {
  @Inject()
  ctx: Context;

  @Inject()
  wepayService: WepayService;

  @Inject()
  weappService: WeappService;

  @Inject()
  orderDiscountInfoService: OrderDiscountInfoService;

  @Inject()
  messageBroadcastService: MessageBroadcastService;

  @Inject()
  logger: ILogger;

  @Inject()
  messageHelper: MessageHelper;

  @Inject()
  servicePhotoService: ServicePhotoService;

  @Inject()
  additionalServiceOrderService: AdditionalServiceOrderService;

  @Inject()
  serviceDurationRecordService: ServiceDurationRecordService;

  constructor() {
    super('订单');
  }
  getModel = () => {
    return Order;
  };

  /**
   * 获取订单日志
   *
   * @param orderId 订单ID
   * @returns 日志
   */
  async getLogs(orderId: number) {
    return await ServiceChangeLog.findAll({
      where: {
        orderId,
      },
      include: [
        {
          model: Customer,
        },
        {
          model: Employee,
        },
      ],
      order: [['createdAt', 'DESC']],
    });
  }

  /**
   * 创建订单
   *
   * @param {CreateOrderData} body 订单数据
   * @memberof OrderService
   */
  async createOrder(body: CreateOrderData) {
    // TODO: 要考虑数据量大的时候使用分页查询
    const { orderDetails, discountInfos, ...createInfo } = body;
    // 生成订单编号：当前时间戳 + 4位随机数
    const timestamp = Date.now().toString();
    const random = Math.floor(Math.random() * 10000)
      .toString()
      .padStart(4, '0');
    createInfo['sn'] = `${timestamp}${random}`;
    // 使用事务创建订单及订单详情
    const result = await Order.sequelize.transaction(async t => {
      console.log('createInfo', createInfo);
      const order = await Order.create(
        {
          ...createInfo,
          serviceTime: !createInfo.serviceTime ? null : createInfo.serviceTime,
        },
        { transaction: t }
      );

      await Promise.all(
        orderDetails.map(async orderDetail => {
          const { addServiceIds, ...addInfo } = orderDetail;

          // 如果没有提供服务名称和价格，从数据库查询并填充
          if (!addInfo.serviceName || !addInfo.servicePrice) {
            const service = await Service.findByPk(addInfo.serviceId);
            if (service) {
              addInfo.serviceName = service.serviceName;
              addInfo.servicePrice = service.basePrice;
            }
          }

          const detail = await OrderDetail.create(
            {
              ...addInfo,
              orderId: order.id,
            },
            { transaction: t }
          );
          if (addServiceIds?.length) {
            console.log('addServiceIds', addServiceIds);
            const flattenedServiceIds = lodash.flattenDeep(addServiceIds);
            console.log('flattenedServiceIds', flattenedServiceIds);
            await detail.$set('additionalServices', flattenedServiceIds, {
              transaction: t,
            });
          }
        })
      );

      ServiceChangeLog.create({
        orderId: order.id,
        changeType: OrderStatusChangeType.下单,
        customerId: createInfo.customerId,
      });

      return order;
    });

    // 处理卡券使用和优惠信息创建
    if (discountInfos && discountInfos.length > 0) {
      try {
        await this.orderDiscountInfoService.processOrderDiscounts(
          result.id,
          discountInfos
        );
      } catch (error) {
        this.logger.error('处理订单优惠信息失败:', error);
      }
    }

    return result;
  }

  // 支付订单
  async payOrder(customerId: number, sn: string) {
    const order = await this.findOne({ where: { sn } });
    if (!order) {
      throw new CustomError('订单不存在');
    }
    if (order.status !== OrderStatus.待付款) {
      throw new CustomError('订单状态不正确');
    }
    await order.update({
      status: OrderStatus.待接单,
    });

    // 广播新订单消息
    await this.messageBroadcastService.broadcastNewOrder(order);

    // 发送用户端订单确认消息
    this.messageHelper.sendOrderConfirmedNotification(customerId, {
      orderNo: order.sn,
      totalAmount: order.totalFee.toString(),
      serviceTime: order.serviceTime
        ? order.serviceTime.toLocaleString()
        : '待确定',
    });

    await ServiceChangeLog.create({
      orderId: order.id,
      changeType: OrderStatusChangeType.付款,
      customerId,
    });

    return true;
  }

  // 申请退款
  async applyRefund(customerId: number, sn: string) {
    const order = await this.findOne({ where: { sn } });
    if (!order) {
      throw new CustomError('订单不存在');
    }
    console.log('order', order);
    if (
      !([OrderStatus.待服务, OrderStatus.已出发] as string[]).includes(
        order.status
      )
    ) {
      throw new CustomError('订单状态不正确');
    }
    await order.update({
      status: OrderStatus.退款中,
    });
    await ServiceChangeLog.create({
      orderId: order.id,
      changeType: OrderStatusChangeType.申请退款,
      customerId,
    });
    return true;
  }

  /**
   * 审核退款
   *
   * @param {object} params 查询条件
   * @param {string} params.sn 订单编号
   * @param {boolean} params.result 审核结果
   * @param {string} [params.reason] 审核原因，审核不通过时必填
   * @param {number} [params.money] 退款金额，审核通过时必填
   * @returns res
   */
  async auditRefund({
    sn,
    result,
    reason,
    money,
  }: {
    sn: string;
    result: boolean;
    reason?: string;
    money?: number;
  }) {
    const order = await this.findOne({ where: { sn } });
    if (!order) {
      throw new CustomError('订单不存在');
    }
    if (order.status !== OrderStatus.退款中) {
      throw new CustomError('订单状态不正确');
    }
    if (result) {
      // 计算总订单金额（包含追加服务）
      const totalOrderAmount =
        Number(order.totalFee) + Number(order.additionalServiceAmount || 0);
      const isFullRefund = Number(money) >= totalOrderAmount;

      // 处理主订单卡券退回（只有全额退款时才退回优惠券）
      if (isFullRefund) {
        try {
          // 检查订单是否有优惠信息
          const refundStatus =
            await this.orderDiscountInfoService.checkOrderRefundStatus(
              order.id
            );
          if (refundStatus.hasDiscountInfos && !refundStatus.allRefunded) {
            await this.orderDiscountInfoService.refundOrderDiscounts(
              order.id,
              `退款审核通过，退款金额${money}元（全额退款）`
            );
          }
        } catch (error) {
          this.logger.error('处理订单卡券退回失败:', error);
        }
      }

      // 处理追加服务退款
      if (order.hasAdditionalServices && isFullRefund) {
        try {
          // 查询该订单的所有已支付追加服务
          const additionalServices =
            await this.additionalServiceOrderService.findAll({
              query: {
                orderDetailId:
                  order.orderDetails?.map(detail => detail.id) || [],
                status: 'paid',
              },
            });

          if (
            additionalServices &&
            additionalServices.list &&
            additionalServices.list.length > 0
          ) {
            for (const additionalService of additionalServices.list) {
              await this.additionalServiceOrderService.refundAdditionalServiceOrder(
                additionalService.id,
                isFullRefund
                  ? `主订单全额退款，退款金额${money}元`
                  : `主订单部分退款，退款金额${money}元（不退优惠券）`,
                isFullRefund // 只有全额退款时才退回优惠券
              );
            }
          }
        } catch (error) {
          this.logger.error('处理追加服务退款失败:', error);
        }
      }

      await this.wepayService.refund(sn, money);
      await order.update({
        status: OrderStatus.已退款,
      });

      // 清除该订单的微信订阅信息
      this.weappService.clearOrderSubscriptions(order.id.toString());

      // 广播取消订单消息
      await this.messageBroadcastService.broadcastCancelOrder(order.id);

      await ServiceChangeLog.create({
        changeType: OrderStatusChangeType.退款,
        orderId: order.id,
        description: `退款成功，退款金额${money}元。操作人：${this.ctx.state.user.name}`,
      });

      // 发送用户端订单退款消息
      this.messageHelper.sendOrderRefundedNotification(order.customerId, {
        orderNo: order.sn,
        refundAmount: money.toString(),
      });
    } else {
      await order.update({
        status: OrderStatus.待服务,
      });
      await ServiceChangeLog.create({
        changeType: OrderStatusChangeType.退款,
        orderId: order.id,
        description: `退款失败，原因：${reason} 操作人：${this.ctx.state.user.name}`,
      });
    }
  }

  /**
   * 取消订单
   * 提前六个小时，扣除20%，提前四个小时，扣除40%，提前两个小时扣除60%，到点取消扣除80%。
   *
   * @param {*} id 订单ID
   * @param customerId 用户ID
   * @param id 订单ID
   * @returns res
   */
  async cancelOrder(customerId: number, id: number) {
    const order = await this.findById(id);
    if (!order) {
      throw new CustomError('订单不存在');
    }
    if (order.status !== OrderStatus.待付款) {
      throw new CustomError('目前仅能自助取消待支付订单，特殊情况请联系客服');
    }

    // 处理主订单卡券退回（取消订单时总是全额退回）
    try {
      // 检查订单是否有优惠信息
      const refundStatus =
        await this.orderDiscountInfoService.checkOrderRefundStatus(id);
      if (refundStatus.hasDiscountInfos && !refundStatus.allRefunded) {
        await this.orderDiscountInfoService.refundOrderDiscounts(
          id,
          customerId ? '用户取消订单' : '系统取消订单'
        );
      }
    } catch (error) {
      this.logger.error('处理订单卡券退回失败:', error);
    }

    // 处理追加服务取消（取消订单时总是退回优惠券）
    if (order.hasAdditionalServices) {
      try {
        // 查询该订单的所有已支付追加服务
        const additionalServices =
          await this.additionalServiceOrderService.findAll({
            query: {
              orderDetailId: order.orderDetails?.map(detail => detail.id) || [],
              status: 'paid',
            },
          });

        if (
          additionalServices &&
          additionalServices.list &&
          additionalServices.list.length > 0
        ) {
          for (const additionalService of additionalServices.list) {
            await this.additionalServiceOrderService.refundAdditionalServiceOrder(
              additionalService.id,
              customerId ? '用户取消订单' : '系统取消订单',
              true // 取消订单时总是退回优惠券
            );
          }
        }
      } catch (error) {
        this.logger.error('处理追加服务取消失败:', error);
      }
    }

    await order.update({
      status: OrderStatus.已取消,
    });

    // 清除该订单的微信订阅信息
    try {
      await this.weappService.clearOrderSubscriptions(order.id.toString());
    } catch (error) {
      this.logger.error('清除订单微信订阅信息失败:', error);
    }

    await ServiceChangeLog.create({
      orderId: order.id,
      changeType: OrderStatusChangeType.取消订单,
      customerId: customerId || null,
      description: customerId ? '用户取消订单' : '系统取消订单',
    });

    // 发送用户端订单取消消息
    this.messageHelper.sendOrderCancelledNotification(order.customerId, {
      orderNo: order.sn,
      reason: customerId ? '' : '，系统自动取消',
    });

    return true;
  }

  /**
   * 删除订单
   * 一般只有具备足够权限的用户，如系统管理员或高级用户才能执行删除订单操作
   * 很多系统限制只能删除特定状态的订单，如未支付的订单。
   * 已支付、配送中、已完成的订单通常不支持删除。
   * 通过在线结账系统处理付款的订单或使用礼品卡付款的订单，只能存档不能删除
   */
  async deleteOrder(id: number) {
    console.log('id', id);
    const order = await this.findById(id);
    if (!order) {
      throw new CustomError('订单不存在');
    }
    if (order.status !== OrderStatus.已取消) {
      throw new CustomError('订单状态不正确');
    }

    // 清除该订单的微信订阅信息
    try {
      await this.weappService.clearOrderSubscriptions(order.id.toString());
    } catch (error) {
      this.logger.error('清除订单微信订阅信息失败:', error);
    }

    return await order.destroy();
    // throw new CustomError('暂不支持删除订单');
  }

  // 查询可接单列表
  async findList(query: any) {
    const { page = 1, pageSize = 10, type, employeeId, ...rest } = query;
    void rest;
    const where_orderDetail = {};

    // 获取员工职位限制的服务类型
    let allowedServiceTypes: string[] = [];
    if (employeeId) {
      const employee = await Employee.findByPk(employeeId);
      if (employee && employee.position) {
        allowedServiceTypes = POSITION_SERVICE_MAPPING[employee.position] || [];
      }
    }

    // 如果员工有职位限制，必须根据职位筛选
    if (allowedServiceTypes.length > 0) {
      // 如果指定了type，先检查是否在员工允许范围内
      if (type) {
        if (!allowedServiceTypes.includes(type)) {
          // 指定的服务类型不在员工职位允许范围内，返回空结果
          return { list: [], total: 0 };
        }
        // 指定的服务类型在允许范围内，使用指定的type
        const services = await Service.findAll({
          where: { published: true },
          attributes: ['id'],
          include: [
            {
              model: ServiceType,
              where: { type },
              attributes: ['id'],
            },
          ],
        });
        where_orderDetail['serviceId'] = services.map(s => s.id);
      } else {
        // 没有指定type，使用员工职位允许的所有服务类型
        const services = await Service.findAll({
          where: { published: true },
          attributes: ['id'],
          include: [
            {
              model: ServiceType,
              where: { type: { [Op.in]: allowedServiceTypes } },
              attributes: ['id'],
            },
          ],
        });
        where_orderDetail['serviceId'] = services.map(s => s.id);
      }

      // 如果没有找到匹配的服务，返回空结果
      if (
        !where_orderDetail['serviceId'] ||
        where_orderDetail['serviceId'].length === 0
      ) {
        return { list: [], total: 0 };
      }
    } else if (type) {
      // 没有员工职位限制，但指定了服务类型
      const services = await Service.findAll({
        where: { published: true },
        attributes: ['id'],
        include: [
          {
            model: ServiceType,
            where: { type },
            attributes: ['id'],
          },
        ],
      });
      where_orderDetail['serviceId'] = services.map(s => s.id);

      if (services.length === 0) {
        return { list: [], total: 0 };
      }
    }
    // 如果既没有员工职位限制，也没有指定服务类型，则不过滤服务

    // 构建订单查询条件
    const orderWhere: any = {
      status: OrderStatus.待接单,
    };

    // 如果提供了员工ID，则只查询指定给该员工的订单或未指定员工的订单
    if (employeeId) {
      orderWhere[Op.or] = [
        { employeeId: employeeId }, // 指定给该员工的订单
        { employeeId: null }, // 未指定员工的订单（系统派单）
      ];
    } else {
      // 如果没有提供员工ID，只查询未指定员工的订单
      orderWhere.employeeId = null;
    }

    const res = await Order.findAndCountAll({
      where: orderWhere,
      offset: (page - 1) * pageSize,
      limit: pageSize,
      order: [['updatedAt', 'DESC']],
      include: [
        {
          model: OrderDetail,
          where: where_orderDetail,
          include: [
            {
              model: AdditionalService,
              through: {
                attributes: [],
              },
            },
            {
              model: Service,
            },
          ],
        },
        {
          model: Customer,
        },
      ],
    });
    return {
      list: res.rows,
      total: res.count,
    };
  }

  // 按状态查询员工名下的订单列表
  async findEmployeeOrders({
    employeeId,
    status,
    page,
    pageSize,
  }: {
    employeeId: number;
    status: OrderStatus[];
    page?: number;
    pageSize?: number;
  }) {
    console.log('status:', status);
    return await this.findAll({
      query: {
        employeeId,
        status,
      },
      offset: page && pageSize ? (page - 1) * pageSize : undefined,
      limit: pageSize || undefined,
      order: [['updatedAt', 'DESC']],
      include: [
        {
          model: OrderDetail,
          include: [
            {
              model: AdditionalService,
              through: {
                attributes: [],
              },
            },
            {
              model: Service,
            },
          ],
        },
        {
          model: Customer,
        },
      ],
    });
  }

  // 修改服务时间
  async updateServiceTime({
    id,
    serviceTime,
    employeeId,
    userType,
  }: {
    id: number;
    serviceTime: Date;
    employeeId?: number;
    userType?: 'admin' | 'employee' | 'customer';
  }) {
    const order = await this.findById(id);
    if (!order) {
      throw new CustomError('订单不存在');
    }
    if (
      !([OrderStatus.待接单, OrderStatus.待服务] as string[]).includes(
        order.status
      )
    ) {
      throw new CustomError('订单状态不正确');
    }
    console.log('serviceTime:', serviceTime);
    await order.update({
      serviceTime,
    });
    this.weappService.sendOrderTimeChangeMessage(order.sn);
    await ServiceChangeLog.create({
      orderId: order.id,
      employeeId,
      changeType: OrderStatusChangeType.修改服务时间,
      description: employeeId
        ? '员工修改服务时间'
        : userType === 'customer'
        ? '用户修改服务时间'
        : '系统修改服务时间',
    });

    // 发送用户端服务时间修改消息
    const modifier = employeeId
      ? '员工'
      : userType === 'customer'
      ? '您'
      : '系统';
    console.log('发送变更服务时间通知： ', serviceTime);
    this.messageHelper.sendOrderTimeChangedNotification(order.customerId, {
      orderNo: order.sn,
      newServiceTime: serviceTime.toLocaleString(),
      modifier,
    });
    return true;
  }

  // 修改服务地址
  async updateServiceAddress({
    id,
    addressData,
    employeeId,
    userType,
  }: {
    id: number;
    addressData: {
      address?: string;
      addressDetail?: string;
      longitude?: number;
      latitude?: number;
      addressRemark?: string;
      addressId?: number | null;
    };
    employeeId?: number;
    userType?: 'admin' | 'employee' | 'customer';
  }) {
    const order = await this.findById(id);
    if (!order) {
      throw new CustomError('订单不存在');
    }

    // 根据用户类型进行状态限制
    if (userType === 'employee' || userType === 'customer') {
      // 用户端和员工端只能在出发前修改地址
      const allowedStatuses = [
        OrderStatus.待付款,
        OrderStatus.待接单,
        OrderStatus.待服务,
      ];
      if (!allowedStatuses.includes(order.status as OrderStatus)) {
        throw new CustomError('当前订单状态不允许修改服务地址');
      }
    }
    // 管理端不做状态限制

    // 构建更新数据
    const updateData: any = {};
    if (addressData.address !== undefined) {
      updateData.address = addressData.address;
    }
    if (addressData.addressDetail !== undefined) {
      updateData.addressDetail = addressData.addressDetail;
    }
    if (addressData.longitude !== undefined) {
      updateData.longitude = addressData.longitude;
    }
    if (addressData.latitude !== undefined) {
      updateData.latitude = addressData.latitude;
    }
    if (addressData.addressRemark !== undefined) {
      updateData.addressRemark = addressData.addressRemark;
    }
    if (addressData.addressId !== undefined) {
      updateData.addressId = addressData.addressId;
    }

    await order.update(updateData);

    // 记录操作日志
    let description = '系统修改服务地址';
    if (userType === 'employee') {
      description = '员工修改服务地址';
    } else if (userType === 'customer') {
      description = '用户修改服务地址';
    } else if (userType === 'admin') {
      description = '管理员修改服务地址';
    }

    await ServiceChangeLog.create({
      orderId: order.id,
      employeeId,
      changeType: OrderStatusChangeType.修改服务地址,
      description,
    });

    return true;
  }

  // 接单
  // accept: '/orders/{orderId}/accept',
  async acceptOrder(id: number, employeeId: number) {
    const order = await this.findById(id);
    if (!order) {
      throw new CustomError('订单不存在');
    }
    if (![OrderStatus.待接单].includes(order.status as OrderStatus)) {
      throw new CustomError('订单状态不正确');
    }

    // 验证员工职位是否可以接此类订单
    await this.validateEmployeePosition(id, employeeId);
    await order.update({
      status: OrderStatus.待服务,
      employeeId,
    });

    // 发送微信模板消息
    this.weappService.sendOrderConfirmationMessage(order.sn);

    // 发送系统内消息通知给员工
    this.messageHelper.sendOrderAcceptedNotification(order.employeeId, {
      orderNo: order.sn,
    });

    // 发送用户端员工接单消息
    const orderWithEmployee = await Order.findByPk(id, {
      include: [Employee],
    });
    if (orderWithEmployee?.employee) {
      this.messageHelper.sendOrderEmployeeAssignedNotification(
        order.customerId,
        {
          orderNo: order.sn,
          employeeName: orderWithEmployee.employee.name,
          employeePhone: orderWithEmployee.employee.phone,
        }
      );
    }

    await ServiceChangeLog.create({
      orderId: order.id,
      changeType: OrderStatusChangeType.接单,
      employeeId,
    });
    return true;
  }

  // 派单
  async deliverOrder(id: number, employeeId: number) {
    const order = await this.findById(id);
    if (!order) {
      throw new CustomError('订单不存在');
    }
    if (![OrderStatus.待接单].includes(order.status as OrderStatus)) {
      throw new CustomError('订单状态不正确');
    }

    // 验证员工职位是否可以接此类订单
    await this.validateEmployeePosition(id, employeeId);
    await order.update({
      status: OrderStatus.待服务,
      employeeId,
    });
    this.weappService.sendOrderConfirmationMessage(order.sn);
    await ServiceChangeLog.create({
      orderId: order.id,
      changeType: OrderStatusChangeType.派单,
      employeeId,
      description: '系统派单',
    });
    return true;
  }

  // 转单
  async transferOrder(id: number, employeeId: number) {
    const order = await this.findById(id);
    if (!order) {
      throw new CustomError('订单不存在');
    }
    if (![OrderStatus.待服务].includes(order.status as OrderStatus)) {
      throw new CustomError('订单状态不正确');
    }

    // 验证员工职位是否可以接此类订单
    await this.validateEmployeePosition(id, employeeId);
    await order.update({
      status: OrderStatus.待服务,
      employeeId,
    });
    await ServiceChangeLog.create({
      orderId: order.id,
      changeType: OrderStatusChangeType.转单,
      employeeId,
      description: '系统转单',
    });
    return true;
  }

  // 出发
  // dispatch: '/orders/{orderId}/dispatch',
  async order_dispatch(id: number, employeeId: number) {
    const order = await this.findById(id);
    if (!order) {
      throw new CustomError('订单不存在');
    }
    // 判断订单是不是这个员工的
    if (order.employeeId !== employeeId) {
      throw new CustomError('订单不属于该员工');
    }
    if (![OrderStatus.待服务].includes(order.status as OrderStatus)) {
      throw new CustomError('订单状态不正确');
    }
    await order.update({
      status: OrderStatus.已出发,
      employeeId,
    });
    this.weappService.sendOrderStatusChangeMessage(
      order.sn,
      '服务人员已出发，请保持联系'
    );

    // 发送用户端员工出发消息
    const orderWithEmployee = await Order.findByPk(id, {
      include: [Employee],
    });
    if (orderWithEmployee?.employee) {
      this.messageHelper.sendOrderDispatchedNotification(order.customerId, {
        orderNo: order.sn,
        employeeName: orderWithEmployee.employee.name,
      });
    }
    await ServiceChangeLog.create({
      orderId: order.id,
      changeType: OrderStatusChangeType.出发,
      employeeId,
    });
    return true;
  }

  // 开始服务
  // start: '/orders/{orderId}/start',
  async order_start(id: number, employeeId: number, beforePhotos?: string[]) {
    const order = await this.findById(id);
    if (!order) {
      throw new CustomError('订单不存在');
    }
    // 判断订单是不是这个员工的
    if (order.employeeId !== employeeId) {
      throw new CustomError('订单不属于该员工');
    }
    if (
      ![OrderStatus.已出发, OrderStatus.待服务].includes(
        order.status as OrderStatus
      )
    ) {
      throw new CustomError('订单状态不正确');
    }
    const actualServiceStartTime = new Date();
    await order.update({
      status: OrderStatus.服务中,
      employeeId,
      actualServiceStartTime,
    });

    // 如果提供了服务前照片，则上传
    if (beforePhotos && beforePhotos.length > 0) {
      try {
        await this.servicePhotoService.uploadBeforePhotos(
          id,
          employeeId,
          beforePhotos
        );
      } catch (error) {
        this.logger.error('上传服务前照片失败:', error);
      }
    }

    this.weappService.sendOrderStatusChangeMessage(
      order.sn,
      '您的订单已开始服务'
    );

    // 发送用户端服务开始消息
    const orderWithDetails = await Order.findByPk(id, {
      include: [
        {
          model: OrderDetail,
          include: [Service],
        },
      ],
    });
    const serviceName =
      orderWithDetails?.orderDetails?.[0]?.serviceName || '服务';
    this.messageHelper.sendOrderStartedNotification(order.customerId, {
      orderNo: order.sn,
      serviceName,
    });
    await ServiceChangeLog.create({
      orderId: order.id,
      changeType: OrderStatusChangeType.开始服务,
      employeeId,
    });
    return true;
  }

  // 完成订单
  // complete: '/orders/{orderId}/complete',
  async order_complete(id: number, employeeId: number, afterPhotos?: string[]) {
    const order = await this.findById(id);
    if (!order) {
      throw new CustomError('订单不存在');
    }
    // 判断订单是不是这个员工的
    if (order.employeeId !== employeeId) {
      throw new CustomError('订单不属于该员工');
    }
    if (![OrderStatus.服务中].includes(order.status as OrderStatus)) {
      throw new CustomError('订单状态不正确');
    }

    // 检查是否存在未付款的附加服务
    const hasUnpaidAdditionalServices =
      await this.checkUnpaidAdditionalServices(id);
    if (hasUnpaidAdditionalServices) {
      throw new CustomError('存在未付款的附加服务，无法完成订单');
    }

    // 统一处理所有需要时长统计的服务
    await this.handleServiceDurationOnOrderComplete(id, employeeId);

    const actualServiceEndTime = new Date();
    let actualServiceDuration = 0;

    // 计算服务时长（分钟）
    if (order.actualServiceStartTime) {
      actualServiceDuration = Math.round(
        (actualServiceEndTime.getTime() -
          order.actualServiceStartTime.getTime()) /
          (1000 * 60)
      );
    }

    await order.update({
      status: OrderStatus.已完成,
      employeeId,
      actualServiceEndTime,
      actualServiceDuration,
    });

    // 完成所有已支付的追加服务
    if (order.hasAdditionalServices) {
      try {
        await this.completeAdditionalServices(id);
      } catch (error) {
        this.logger.error('完成追加服务失败:', error);
      }
    }

    // 如果提供了服务后照片，则上传
    if (afterPhotos && afterPhotos.length > 0) {
      try {
        await this.servicePhotoService.uploadAfterPhotos(
          id,
          employeeId,
          afterPhotos
        );
      } catch (error) {
        this.logger.error('上传服务后照片失败:', error);
      }
    }

    // 发送微信模板消息
    this.weappService.sendOrderStatusChangeMessage(
      order.sn,
      '您的订单服务已完成'
    );

    // 发送系统内消息通知给员工
    this.messageHelper.sendOrderCompletedNotification(order.employeeId, {
      orderNo: order.sn,
    });

    // 发送用户端服务完成消息
    const orderWithDetails = await Order.findByPk(id, {
      include: [
        {
          model: OrderDetail,
          include: [Service],
        },
      ],
    });
    const serviceName =
      orderWithDetails?.orderDetails?.[0]?.serviceName || '服务';
    this.messageHelper.sendOrderFinishedNotification(order.customerId, {
      orderNo: order.sn,
      serviceName,
    });

    // 清除该订单的微信订阅信息
    try {
      await this.weappService.clearOrderSubscriptions(order.id.toString());
    } catch (error) {
      this.logger.error('清除订单微信订阅信息失败:', error);
    }

    // this.weappService.sendOrderCompletionMessage(order.sn);
    await ServiceChangeLog.create({
      orderId: order.id,
      changeType: OrderStatusChangeType.完成订单,
      employeeId,
    });
    return true;
  }

  /**
   * 完成订单的所有已支付追加服务
   */
  private async completeAdditionalServices(orderId: number) {
    const order = await Order.findByPk(orderId, {
      include: [OrderDetail],
    });

    if (!order || !order.orderDetails) {
      return;
    }

    // 查询该订单所有已支付的追加服务
    const additionalServices = await this.additionalServiceOrderService.findAll(
      {
        query: {
          orderDetailId: order.orderDetails.map(detail => detail.id),
          status: 'paid',
        },
      }
    );

    if (
      !additionalServices ||
      !additionalServices.list ||
      additionalServices.list.length === 0
    ) {
      return;
    }

    // 批量完成追加服务
    for (const additionalService of additionalServices.list) {
      try {
        await this.additionalServiceOrderService.completeAdditionalService(
          additionalService.id
        );
      } catch (error) {
        this.logger.error(`完成追加服务${additionalService.id}失败:`, error);
      }
    }
  }

  // 退款订单
  // 平台或商家会规定具体的退款政策，如按比例退款、全额退款等。比如美团民宿，房客在 "全额退款日" 之前取消预订，可全额退还线上支付的全部订单金额及押金费用，之后取消则按房东规定的交易规则执行，扣除部分 / 全部订单金额后返还剩余部分

  /**
   * 管理员申请退款 - 可以对任何状态的订单申请退款
   * @param sn 订单编号
   * @param operatorId 操作员ID
   * @param reason 退款原因
   */
  async adminApplyRefund(sn: string, operatorId: number, reason?: string) {
    const order = await this.findOne({ where: { sn } });
    if (!order) {
      throw new CustomError('订单不存在');
    }

    // 管理员可以对任何状态的订单申请退款，除了已经是退款相关状态的
    if (
      [OrderStatus.退款中, OrderStatus.已退款].includes(
        order.status as OrderStatus
      )
    ) {
      throw new CustomError('订单已在退款流程中或已退款');
    }

    await order.update({
      status: OrderStatus.退款中,
    });

    await ServiceChangeLog.create({
      orderId: order.id,
      changeType: OrderStatusChangeType.申请退款,
      customerId: operatorId, // 使用操作员ID
      description: `管理员申请退款${reason ? `，原因：${reason}` : ''}`,
    });

    return true;
  }

  /**
   * 管理员审核退款 - 无状态检查，可以对任何状态的订单进行退款审核
   * @param params 审核参数
   */
  async adminAuditRefund({
    sn,
    result,
    reason,
    money,
  }: {
    sn: string;
    result: boolean;
    reason?: string;
    money?: number;
  }) {
    const order = await this.findOne({ where: { sn } });
    if (!order) {
      throw new CustomError('订单不存在');
    }

    // 管理员审核退款无需状态检查，可以对任何状态的订单操作
    if (result) {
      // 计算总订单金额（包含追加服务）
      const totalOrderAmount =
        Number(order.totalFee) + Number(order.additionalServiceAmount || 0);
      const isFullRefund = Number(money) >= totalOrderAmount;

      // 处理主订单卡券退回（只有全额退款时才退回优惠券）
      if (isFullRefund) {
        try {
          // 检查订单是否有优惠信息
          const refundStatus =
            await this.orderDiscountInfoService.checkOrderRefundStatus(
              order.id
            );
          if (refundStatus.hasDiscountInfos && !refundStatus.allRefunded) {
            await this.orderDiscountInfoService.refundOrderDiscounts(
              order.id,
              `管理员退款审核通过，退款金额${money}元（全额退款）`
            );
          }
        } catch (error) {
          this.logger.error('处理订单卡券退回失败:', error);
        }
      }

      // 调用管理员专用的微信退款方法（无状态检查）
      await this.wepayService.adminRefund(sn, money);

      // 只有当前状态不是已退款时才更新状态
      if (order.status !== OrderStatus.已退款) {
        await order.update({
          status: OrderStatus.已退款,
        });
      }

      // 清除该订单的微信订阅信息
      this.weappService.clearOrderSubscriptions(order.id.toString());

      // 广播取消订单消息
      await this.messageBroadcastService.broadcastCancelOrder(order.id);

      await ServiceChangeLog.create({
        changeType: OrderStatusChangeType.退款,
        orderId: order.id,
        description: `管理员退款审核通过，退款金额${money}元。${
          reason ? `原因：${reason}` : ''
        }`,
      });

      // 发送用户端订单退款消息
      this.messageHelper.sendOrderRefundedNotification(order.customerId, {
        orderNo: order.sn,
        refundAmount: money.toString(),
      });
    } else {
      // 审核不通过，恢复到之前的状态（管理员操作通常不会拒绝，但保留逻辑）
      await order.update({
        status: OrderStatus.待服务,
      });
    }

    return true;
  }

  /**
   * 管理员删除订单 - 可以删除任何状态的订单
   * 删除前会检查付款状态，如有付款会先进行退款
   * @param id 订单ID
   * @param operatorId 操作员ID
   * @param reason 删除原因
   */
  async adminDeleteOrder(id: number, operatorId: number, reason?: string) {
    // 开启事务
    const transaction = await this.getModel().sequelize.transaction();

    try {
      // 1. 查询订单及相关数据
      const order = await this.findOne({
        where: { id },
        include: [
          {
            model: OrderDetail,
            include: [
              {
                model: AdditionalServiceOrder,
                required: false,
              },
            ],
          },
        ],
        transaction,
      });

      if (!order) {
        throw new CustomError('订单不存在');
      }

      this.logger.info(`开始删除订单${order.sn}`, {
        orderId: id,
        orderStatus: order.status,
        operatorId,
        reason,
      });

      // 2. 收集所有追加服务订单（包括所有状态）
      const allAdditionalServices = [];
      if (order.orderDetails) {
        for (const detail of order.orderDetails) {
          if (detail.additionalServiceOrders) {
            allAdditionalServices.push(...detail.additionalServiceOrders);
          }
        }
      }

      // 3. 检查主订单是否已付款
      const isMainOrderPaid = ![
        OrderStatus.待付款,
        OrderStatus.已取消,
      ].includes(order.status as OrderStatus);

      // 4. 筛选需要退款的追加服务（已付款状态，包括PAID和COMPLETED）
      const paidAdditionalServices = allAdditionalServices.filter(service =>
        [
          AdditionalServiceOrderStatus.PAID,
          AdditionalServiceOrderStatus.COMPLETED,
        ].includes(service.status)
      );

      // 详细记录所有追加服务的状态
      this.logger.info(`订单${order.sn}追加服务详情`, {
        allAdditionalServices: allAdditionalServices.map(service => ({
          id: service.id,
          sn: service.sn,
          status: service.status,
          totalFee: service.totalFee,
          isPaid: service.status === AdditionalServiceOrderStatus.PAID,
        })),
      });

      this.logger.info(`订单${order.sn}付款状态检查`, {
        isMainOrderPaid,
        totalAdditionalServices: allAdditionalServices.length,
        paidAdditionalServices: paidAdditionalServices.length,
        paidAdditionalServicesIds: paidAdditionalServices.map(s => s.id),
      });

      // 5. 处理退款逻辑
      if (isMainOrderPaid || paidAdditionalServices.length > 0) {
        this.logger.info(
          `管理员删除订单${order.sn}，检测到付款信息，开始退款流程`,
          {
            isMainOrderPaid,
            paidAdditionalServicesCount: paidAdditionalServices.length,
            paidAdditionalServicesIds: paidAdditionalServices.map(s => s.id),
          }
        );

        // 先处理追加服务退款（在事务外执行，避免微信接口超时影响事务）
        if (paidAdditionalServices.length > 0) {
          this.logger.info(
            `开始退款${paidAdditionalServices.length}个追加服务订单`
          );

          for (const additionalService of paidAdditionalServices) {
            this.logger.info(
              `正在退款追加服务订单 ${additionalService.id} (${additionalService.sn})`
            );

            try {
              await this.additionalServiceOrderService.adminRefundAdditionalServiceOrder(
                additionalService.id,
                `管理员删除订单前自动退款${
                  reason ? `，删除原因：${reason}` : ''
                }`,
                true // 删除时总是退回优惠券
              );
              this.logger.info(`追加服务订单 ${additionalService.id} 退款成功`);
            } catch (error) {
              this.logger.error(
                `追加服务订单 ${additionalService.id} 退款失败:`,
                error
              );
              throw new CustomError(`追加服务退款失败：${error.message}`);
            }
          }

          this.logger.info(
            `已成功退款${paidAdditionalServices.length}个追加服务订单`
          );
        }

        // 处理主订单退款
        if (isMainOrderPaid) {
          const mainOrderRefundAmount = Number(order.totalFee);

          if (mainOrderRefundAmount === 0) {
            // 0元订单直接更新状态
            await order.update({ status: OrderStatus.已退款 }, { transaction });
            await ServiceChangeLog.create(
              {
                changeType: OrderStatusChangeType.退款,
                orderId: order.id,
                description: `管理员删除订单前自动退款，主订单金额为0${
                  reason ? `，删除原因：${reason}` : ''
                }`,
              },
              { transaction }
            );
          } else {
            // 非0元订单需要调用微信退款
            if (
              ![OrderStatus.退款中, OrderStatus.已退款].includes(
                order.status as OrderStatus
              )
            ) {
              await this.adminApplyRefund(
                order.sn,
                operatorId,
                `删除订单前自动申请退款${reason ? `，删除原因：${reason}` : ''}`
              );
            }

            // 执行退款审核（自动通过）- 管理员操作无需状态检查
            await this.adminAuditRefund({
              sn: order.sn,
              result: true,
              reason: `管理员删除订单前自动退款${
                reason ? `，删除原因：${reason}` : ''
              }`,
              money: mainOrderRefundAmount,
            });
          }

          this.logger.info(
            `主订单退款完成，退款金额：${mainOrderRefundAmount}元`
          );
        }

        // 重新获取订单状态，确保退款完成
        await order.reload({ transaction });
        if (
          ![OrderStatus.已退款, OrderStatus.已取消].includes(
            order.status as OrderStatus
          )
        ) {
          throw new CustomError('退款未完成，无法删除订单');
        }
      }

      // 6. 删除所有追加服务订单记录（在事务中）
      if (allAdditionalServices.length > 0) {
        await AdditionalServiceOrder.destroy({
          where: {
            id: allAdditionalServices.map(service => service.id),
          },
          transaction,
        });
        this.logger.info(
          `删除了${allAdditionalServices.length}个追加服务订单记录`
        );
      }

      // 7. 记录删除操作日志
      await ServiceChangeLog.create(
        {
          orderId: order.id,
          changeType: '删除订单' as any,
          customerId: operatorId,
          description: `管理员删除订单${reason ? `，原因：${reason}` : ''}${
            isMainOrderPaid || paidAdditionalServices.length > 0
              ? '（已自动退款）'
              : ''
          }`,
        },
        { transaction }
      );

      // 8. 删除订单
      await order.destroy({ transaction });

      // 9. 提交事务
      await transaction.commit();

      // 10. 清除微信订阅信息（在事务外，失败不影响删除）
      try {
        await this.weappService.clearOrderSubscriptions(order.id.toString());
      } catch (error) {
        this.logger.error('清除订单微信订阅信息失败:', error);
      }

      this.logger.info(`管理员成功删除订单${order.sn}`, {
        orderId: id,
        operatorId,
        reason,
        hadPayment: isMainOrderPaid || paidAdditionalServices.length > 0,
      });

      return true;
    } catch (error) {
      // 回滚事务
      await transaction.rollback();

      this.logger.error('管理员删除订单失败:', error);

      // 检查是否是微信退款成功但数据库更新失败的情况
      if (error.message && error.message.includes('微信退款')) {
        throw new CustomError(
          `删除订单失败：${error.message}。请检查订单状态，如微信已退款但订单状态未更新，请手动处理。`
        );
      }

      throw new CustomError(`删除订单失败：${error.message}`);
    }
  }

  /**
   * 管理员取消订单 - 可以取消任何状态的订单
   * @param id 订单ID
   * @param operatorId 操作员ID
   * @param reason 取消原因
   */
  async adminCancelOrder(id: number, operatorId: number, reason?: string) {
    const order = await this.findById(id);
    if (!order) {
      throw new CustomError('订单不存在');
    }

    // 管理员可以取消任何状态的订单，除了已经取消或已退款的
    if (
      [OrderStatus.已取消, OrderStatus.已退款].includes(
        order.status as OrderStatus
      )
    ) {
      throw new CustomError('订单已取消或已退款，无法再次取消');
    }

    // 处理主订单卡券退回（管理员取消订单时总是全额退回）
    try {
      const refundStatus =
        await this.orderDiscountInfoService.checkOrderRefundStatus(id);
      if (refundStatus.hasDiscountInfos && !refundStatus.allRefunded) {
        await this.orderDiscountInfoService.refundOrderDiscounts(
          id,
          `管理员取消订单${reason ? `，原因：${reason}` : ''}`
        );
      }
    } catch (error) {
      this.logger.error('处理订单卡券退回失败:', error);
    }

    await order.update({
      status: OrderStatus.已取消,
    });

    await ServiceChangeLog.create({
      orderId: order.id,
      changeType: OrderStatusChangeType.取消订单,
      customerId: operatorId,
      description: `管理员取消订单${reason ? `，原因：${reason}` : ''}`,
    });

    // 清除该订单的微信订阅信息
    try {
      await this.weappService.clearOrderSubscriptions(order.id.toString());
    } catch (error) {
      this.logger.error('清除订单微信订阅信息失败:', error);
    }

    return true;
  }

  // 导出订单

  /**
   * 验证员工职位是否可以接指定订单
   */
  private async validateEmployeePosition(orderId: number, employeeId: number) {
    const employee = await Employee.findByPk(employeeId);
    if (!employee) {
      throw new CustomError('员工不存在');
    }

    if (employee.position) {
      const allowedServiceTypes =
        POSITION_SERVICE_MAPPING[employee.position] || [];
      if (allowedServiceTypes.length > 0) {
        // 获取订单中的服务类型
        const orderWithDetails = await Order.findByPk(orderId, {
          include: [
            {
              model: OrderDetail,
              include: [
                {
                  model: Service,
                  include: [
                    {
                      model: ServiceType,
                    },
                  ],
                },
              ],
            },
          ],
        });

        if (orderWithDetails && orderWithDetails.orderDetails) {
          const orderServiceTypes = orderWithDetails.orderDetails
            .map(detail => detail.service?.serviceType?.type)
            .filter(Boolean);

          // 检查是否有不允许的服务类型
          const hasDisallowedService = orderServiceTypes.some(
            serviceType => !allowedServiceTypes.includes(serviceType)
          );

          if (hasDisallowedService) {
            const positionName =
              employee.position === 'XI_HU_SHI' ? '洗护师' : '美容师';
            throw new CustomError(`${positionName}不能接此类订单`);
          }
        }
      }
    }
  }

  /**
   * 检查是否存在未付款的附加服务
   * 未付款状态包括：待确认、已确认、待付款
   *
   * 此方法被多个服务共享使用，确保订单完成逻辑的一致性
   */
  async checkUnpaidAdditionalServices(orderId: number): Promise<boolean> {
    // 通过订单详情获取所有附加服务订单
    const orderDetails = await OrderDetail.findAll({
      where: { orderId },
      include: [
        {
          model: AdditionalServiceOrder,
          where: {
            status: [
              AdditionalServiceOrderStatus.PENDING_CONFIRM,
              AdditionalServiceOrderStatus.CONFIRMED,
              AdditionalServiceOrderStatus.PENDING_PAYMENT,
            ],
          },
          required: false, // 左连接，如果没有未付款的附加服务则不返回
        },
      ],
    });

    // 检查是否存在未付款的附加服务
    for (const orderDetail of orderDetails) {
      if (
        orderDetail.additionalServiceOrders &&
        orderDetail.additionalServiceOrders.length > 0
      ) {
        this.logger.info(`订单 ${orderId} 存在未付款的附加服务:`, {
          orderDetailId: orderDetail.id,
          unpaidServices: orderDetail.additionalServiceOrders.map(service => ({
            id: service.id,
            sn: service.sn,
            status: service.status,
            totalFee: service.totalFee,
          })),
        });
        return true;
      }
    }

    return false;
  }

  /**
   * 处理订单完成时的服务时长统计
   * 对所有需要统计时长的服务进行检查：
   * - 主服务：必然触发结束计时（有开始时间但没有结束时间的，立即结束）
   * - 增项服务：只有已经开始的才会触发结束计时（没有开始时间的，跳过处理）
   *
   * 处理两种类型的增项服务：
   * 1. 主订单中的增项服务（通过orderDetailId关联）
   * 2. 追加服务中的增项服务（通过additionalServiceOrderId关联）
   */
  private async handleServiceDurationOnOrderComplete(
    orderId: number,
    employeeId: number
  ): Promise<void> {
    try {
      // 查询订单下所有未结束的服务记录
      const records = await ServiceDurationRecord.findAll({
        where: {
          orderId,
          endTime: null, // 只查询未结束的记录
        },
        include: [
          {
            model: AdditionalService,
            attributes: ['id', 'name', 'needDurationTracking'],
            required: false, // 左连接，因为主服务没有关联AdditionalService
          },
          {
            model: OrderDetail,
            attributes: ['id'],
            required: false,
            include: [
              {
                model: AdditionalService,
                through: { attributes: [] }, // 排除中间表字段
                attributes: ['id', 'name', 'needDurationTracking'],
                required: false,
              },
            ],
          },
          {
            model: AdditionalServiceOrder,
            attributes: ['id'],
            required: false,
          },
        ],
      });

      if (records.length === 0) {
        this.logger.info(`订单 ${orderId} 没有需要处理的时长统计记录`);
        return;
      }

      // 过滤出需要统计时长的记录
      const recordsToProcess = [];

      for (const record of records) {
        let needTracking = false;

        if (record.recordType === 'main_service') {
          // 主服务默认需要统计时长
          needTracking = true;
        } else if (record.recordType === 'additional_service') {
          // 增项服务需要检查 needDurationTracking 字段
          if (record.additionalServiceOrderId) {
            // 追加服务中的增项服务：直接通过additionalService关联获取
            needTracking =
              record.additionalService?.needDurationTracking === true;
          } else if (record.orderDetailId && record.additionalServiceId) {
            // 主订单中的增项服务：通过orderDetail的additionalServices关联获取
            const additionalService =
              record.orderDetail?.additionalServices?.find(
                as => as.id === record.additionalServiceId
              );
            needTracking = additionalService?.needDurationTracking === true;
          }
        }

        if (needTracking) {
          recordsToProcess.push(record);
        }
      }

      this.logger.info(
        `订单 ${orderId} 需要处理 ${recordsToProcess.length} 条时长统计记录`
      );

      // 处理每条记录
      for (const record of recordsToProcess) {
        const serviceType = record.additionalServiceOrderId
          ? '追加服务中的增项服务'
          : record.additionalServiceId
          ? '主订单中的增项服务'
          : '主服务';

        // 主服务必然触发结束计时，增项服务只有已经开始的才会触发结束计时
        const isMainService = record.recordType === 'main_service';
        const shouldEndService = isMainService || record.startTime;

        if (shouldEndService && record.startTime) {
          // 有开始时间的，立即结束
          const endTime = new Date();
          const duration = Math.round(
            (endTime.getTime() - record.startTime.getTime()) / (1000 * 60)
          );

          await record.update({
            endTime,
            duration,
            remark: record.remark
              ? `${record.remark}; 订单完成时自动结束`
              : '订单完成时自动结束',
          });

          this.logger.info(
            `自动结束服务记录 ${record.id}: ${record.serviceName} (${serviceType}), 时长: ${duration}分钟`
          );
        } else {
          // 没有开始时间的，跳过处理（主要是增项服务）
          this.logger.info(
            `跳过未开始的服务记录 ${record.id}: ${record.serviceName} (${serviceType})`
          );
        }
      }

      this.logger.info(`订单 ${orderId} 时长统计处理完成`);
    } catch (error) {
      this.logger.error(`处理订单 ${orderId} 时长统计失败:`, error);
      // 不抛出错误，避免影响订单完成流程
    }
  }
}
