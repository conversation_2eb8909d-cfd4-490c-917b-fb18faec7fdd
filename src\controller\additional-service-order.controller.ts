import {
  Controller,
  Get,
  Post,
  Del,
  Body,
  Param,
  Query,
  Inject,
} from '@midwayjs/core';
import {
  AdditionalServiceOrderService,
  CreateAdditionalServiceOrderData,
} from '../service/additional-service-order.service';
import { CustomError } from '../error/custom.error';
import {
  AdditionalServiceOrder,
  AdditionalServiceOrderStatus,
} from '../entity/additional-service-order.entity';
import { AdditionalServiceOrderDetail } from '../entity/additional-service-order-detail.entity';
import { AdditionalServiceDiscountInfo } from '../entity/additional-service-discount-info.entity';
import { OrderDetail } from '../entity/order-detail.entity';
import { Order } from '../entity/order.entity';
import { Customer } from '../entity/customer.entity';
import { Employee } from '../entity/employee.entity';
import { Service } from '../entity/service.entity';
import { AdditionalService } from '../entity/additional-service.entity';
import { DiscountType } from '../entity/order-discount-info.entity';
import { ServiceDurationRecord } from '../entity/service-duration-record.entity';

@Controller('/order-details/:orderDetailId/additional-services')
export class AdditionalServiceOrderController {
  @Inject()
  service: AdditionalServiceOrderService;

  @Post('/', { summary: '创建追加服务申请' })
  async create(
    @Param('orderDetailId') orderDetailId: number,
    @Body()
    body: {
      customerId: number;
      services: {
        serviceId: number;
        quantity: number;
      }[];
      discountInfos?: {
        discountType: DiscountType;
        discountId: number;
        discountAmount: number;
      }[];
      remark?: string;
    }
  ) {
    const { customerId, services, discountInfos, remark } = body;

    if (!customerId) {
      throw new CustomError('客户ID不能为空');
    }

    if (!services || services.length === 0) {
      throw new CustomError('追加服务不能为空');
    }

    // 验证服务数据
    for (const serviceItem of services) {
      if (!serviceItem.serviceId) {
        throw new CustomError('服务ID不能为空');
      }
      if (!serviceItem.quantity || serviceItem.quantity <= 0) {
        throw new CustomError('服务数量必须大于0');
      }
    }

    const createData: CreateAdditionalServiceOrderData = {
      orderDetailId,
      customerId,
      services,
      discountInfos,
      remark,
    };

    return await this.service.createAdditionalServiceOrder(createData);
  }

  @Get('/', { summary: '查询追加服务列表' })
  async list(
    @Param('orderDetailId') orderDetailId: number,
    @Query('status') status?: string
  ) {
    const where: any = { orderDetailId };

    if (status) {
      where.status = status;
    }

    // 1. 查询主订单时选择的增项服务
    const orderDetail = await OrderDetail.findByPk(orderDetailId, {
      attributes: ['id', 'orderId', 'serviceName'], // 订单详情基本信息
      include: [
        {
          // 第一层：直接关联的增项服务 (通过 order_detail_additional 中间表)
          // 这是用户在下单时选择的增项服务
          model: AdditionalService,
          through: {
            attributes: [], // 不返回中间表字段 (order_detail_additional)
          },
          attributes: [
            'id', // 增项服务ID
            'name', // 增项服务名称
            'logo', // 服务图标
            'price', // 服务价格
            'type', // 服务类型
            'description', // 服务说明
            'duration', // 服务时长
            'needDurationTracking', // 是否需要计时
          ],
          include: [
            {
              // 第二层：服务时长记录表 (service_duration_records)
              // 查询该增项服务的计时记录（仅限主订单的增项服务）
              model: ServiceDurationRecord,
              required: false, // 左连接，没有记录也要返回
              where: {
                orderDetailId: orderDetailId, // 限定订单详情
                recordType: 'additional_service', // 记录类型为增项服务
                additionalServiceOrderId: null, // 主订单增项服务（非追加服务）
              },
              attributes: ['id', 'startTime', 'endTime'], // 计时记录基本信息
            },
          ],
        },
        {
          // 第一层：关联的主服务信息 (services 表)
          // 获取订单详情对应的主服务信息
          model: Service,
          attributes: ['id', 'serviceName', 'basePrice'], // 主服务基本信息
          include: [
            {
              // 第二层：主服务可选的增项服务 (通过 service_additional 中间表)
              // 这里查询的是该主服务可以选择哪些增项服务（用于前端展示可选项）
              model: AdditionalService,
              through: {
                attributes: [], // 不返回中间表字段 (service_additional)
              },
              attributes: [
                'id', // 增项服务ID
                'name', // 增项服务名称
                'logo', // 服务图标
                'price', // 服务价格
                'type', // 服务类型
                'description', // 服务说明
                'duration', // 服务时长
                'needDurationTracking', // 是否需要计时
              ],
            },
          ],
        },
      ],
    });

    // 2. 查询后续追加的服务订单
    const additionalServiceOrders = await AdditionalServiceOrder.findAll({
      where,
      include: [
        {
          // 第一层：追加服务订单明细表 (additional_service_order_details)
          // 包含：订单明细ID、服务ID、服务名称、价格、数量等基本信息
          // 注意：这里的 serviceId 实际存储的是 AdditionalService 的 ID，不是 Service 的 ID
          model: AdditionalServiceOrderDetail,
          required: false, // 左连接，即使没有明细也返回主记录
          attributes: [
            'id', // 明细ID
            'serviceId', // 关联的增项服务ID（实际存储的是AdditionalService的ID）
            'serviceName', // 服务名称（冗余字段）
            'servicePrice', // 服务价格（冗余字段）
            'quantity', // 数量
          ],
          include: [
            {
              // 第二层：直接关联增项服务表 (additional_services)
              // 根据 serviceId 直接查询对应的增项服务信息
              model: AdditionalService,
              required: false, // 左连接，可能没有对应的增项服务记录
              attributes: [
                'id', // 增项服务ID
                'name', // 增项服务名称
                'logo', // 服务图标
                'price', // 服务价格
                'type', // 服务类型
                'description', // 服务说明
                'duration', // 服务时长
                'needDurationTracking', // 是否需要计时（关键字段）
              ],
            },
          ],
        },
        {
          // 追加服务折扣信息表 (additional_service_discount_infos)
          // 包含：权益卡抵扣、优惠券抵扣等折扣信息
          model: AdditionalServiceDiscountInfo,
          required: false, // 左连接，可能没有折扣信息
        },
        {
          // 客户信息表 (customers)
          // 获取下单客户的基本信息
          model: Customer,
          required: false, // 左连接
          attributes: ['id', 'nickname', 'phone'],
        },
        {
          // 员工信息表 (employees)
          // 获取服务员工的基本信息
          model: Employee,
          required: false, // 左连接
          attributes: ['id', 'name', 'phone'],
        },
      ],
      order: [['updatedAt', 'DESC']], // 按更新时间倒序排列
    });

    // 3. 为主订单增项服务添加计时状态
    // 处理用户在下单时选择的增项服务，为每个服务添加计时状态信息
    const originalAdditionalServicesWithStatus = (
      orderDetail?.additionalServices || []
    ).map(service => {
      // 初始化计时状态
      let durationStatus = 'not_started'; // 默认未开始
      let recordId = null;

      // 如果该增项服务需要计时且有计时记录
      if (
        service.needDurationTracking &&
        service.serviceDurationRecords?.length > 0
      ) {
        const record = service.serviceDurationRecords[0]; // 主订单增项服务应该只有一条记录
        recordId = record.id;

        // 根据计时记录判断状态
        if (record.startTime && record.endTime) {
          durationStatus = 'completed'; // 已完成（有开始和结束时间）
        } else if (record.startTime) {
          durationStatus = 'in_progress'; // 进行中（只有开始时间）
        }
        // 如果没有开始时间，保持 'not_started' 状态
      }

      return {
        ...service.toJSON(),
        durationStatus, // 计时状态：not_started | in_progress | completed
        recordId, // 计时记录ID（用于后续操作）
      };
    });

    // 4. 处理追加服务订单，展开多个相同增项服务
    // 将追加服务订单中的服务按数量展开，每个数量对应一个独立的服务项
    const processedAdditionalServiceOrders = await Promise.all(
      additionalServiceOrders.map(async order => {
        const processedDetails = [];

        // 遍历追加服务订单的每个明细
        for (const detail of order.details || []) {
          console.log(
            `正在处理追加服务订单 ${order.id} 的明细 ${JSON.stringify(detail)}`
          );

          // 获取增项服务信息
          // 现在可以直接从关联获取增项服务信息
          const additionalService = detail.additionalService;
          if (additionalService) {
            // 查询该增项服务在当前追加服务订单中的所有时长记录
            // 一个增项服务如果数量>1，会有多条计时记录，通过sequenceNumber区分
            const durationRecords = await ServiceDurationRecord.findAll({
              where: {
                recordType: 'additional_service', // 记录类型：增项服务
                additionalServiceOrderId: order.id, // 限定追加服务订单
                additionalServiceId: additionalService.id, // 限定增项服务
              },
              order: [['sequenceNumber', 'ASC']], // 按序号排序
              attributes: [
                'id', // 记录ID
                'sequenceNumber', // 序号（用于区分同一服务的多次记录）
                'startTime', // 开始时间
                'endTime', // 结束时间
                'duration', // 服务时长
                'remark', // 备注
              ],
            });

            // 根据数量展开成多个独立的服务项
            // 例如：数量为3的"局部去油"会展开成3个独立的服务项，每个都可以独立计时
            for (let i = 1; i <= detail.quantity; i++) {
              // 查找对应序号的计时记录
              const durationRecord = durationRecords.find(
                record => record.sequenceNumber === i
              );

              // 计算计时状态
              let durationStatus = 'not_started'; // 默认未开始
              if (additionalService.needDurationTracking) {
                // 如果需要计时，根据计时记录判断状态
                if (durationRecord) {
                  if (durationRecord.startTime && durationRecord.endTime) {
                    durationStatus = 'completed'; // 已完成（有开始和结束时间）
                  } else if (durationRecord.startTime) {
                    durationStatus = 'in_progress'; // 进行中（只有开始时间）
                  }
                  // 没有开始时间则保持 'not_started'
                }
              } else {
                durationStatus = 'not_required'; // 不需要统计时长
              }

              processedDetails.push({
                id: `${detail.id}_${i}`, // 使用复合ID确保唯一性
                originalDetailId: detail.id,
                serviceId: detail.serviceId,
                serviceName: detail.serviceName,
                servicePrice: detail.servicePrice,
                sequenceNumber: i,
                totalQuantity: detail.quantity,
                // 直接添加增项服务的关键字段，避免嵌套结构
                additionalServiceId: additionalService.id,
                additionalServiceName: additionalService.name,
                logo: additionalService.logo,
                price: additionalService.price,
                type: additionalService.type,
                description: additionalService.description,
                duration: additionalService.duration,
                needDurationTracking: additionalService.needDurationTracking,
                durationStatus,
                durationRecord: durationRecord
                  ? {
                      id: durationRecord.id,
                      sequenceNumber: durationRecord.sequenceNumber,
                      startTime: durationRecord.startTime,
                      endTime: durationRecord.endTime,
                      duration: durationRecord.duration,
                      remark: durationRecord.remark,
                    }
                  : null,
              });
            }
          }
        }

        return {
          ...order.toJSON(),
          details: processedDetails,
          originalDetails: order.details, // 保留原始明细数据
        };
      })
    );

    // 5. 组合返回结果
    return {
      // 主订单时选择的增项服务（包含计时状态）
      originalAdditionalServices: originalAdditionalServicesWithStatus,
      // 后续追加的服务订单（展开多个相同增项服务）
      additionalServiceOrders: processedAdditionalServiceOrders,
      // 统计信息
      summary: {
        originalCount: orderDetail?.additionalServices?.length || 0,
        additionalOrdersCount: additionalServiceOrders.length,
        totalCount:
          (orderDetail?.additionalServices?.length || 0) +
          additionalServiceOrders.length,
      },
    };
  }

  @Get('/:id', { summary: '查询追加服务详情' })
  async show(
    @Param('orderDetailId') orderDetailId: number,
    @Param('id') id: number
  ) {
    const additionalServiceOrder = await AdditionalServiceOrder.findOne({
      where: {
        id,
        orderDetailId,
      },
      include: [
        {
          model: AdditionalServiceOrderDetail,
          // 暂时移除错误的 Service 关联，等重启项目后使用正确的 AdditionalService 关联
        },
        {
          model: AdditionalServiceDiscountInfo,
        },
        {
          model: OrderDetail,
          include: [
            {
              model: Order,
              include: [Customer, Employee],
            },
          ],
        },
        {
          model: Customer,
          attributes: ['id', 'nickname', 'phone'],
        },
        {
          model: Employee,
          attributes: ['id', 'name', 'phone'],
        },
      ],
    });

    if (!additionalServiceOrder) {
      throw new CustomError('追加服务订单不存在');
    }

    return additionalServiceOrder;
  }

  @Post('/:id/confirm', { summary: '员工确认追加服务' })
  async confirm(
    @Param('orderDetailId') orderDetailId: number,
    @Param('id') id: number,
    @Body('employeeId') employeeId: number
  ) {
    if (!employeeId) {
      throw new CustomError('员工ID不能为空');
    }

    // 验证追加服务是否属于该订单详情
    const additionalServiceOrder = await AdditionalServiceOrder.findOne({
      where: {
        id,
        orderDetailId,
      },
    });

    if (!additionalServiceOrder) {
      throw new CustomError('追加服务订单不存在');
    }

    return await this.service.confirmAdditionalService(id, employeeId);
  }

  @Post('/:id/reject', { summary: '员工拒绝追加服务' })
  async reject(
    @Param('orderDetailId') orderDetailId: number,
    @Param('id') id: number,
    @Body()
    body: {
      employeeId: number;
      rejectReason: string;
    }
  ) {
    const { employeeId, rejectReason } = body;

    if (!employeeId) {
      throw new CustomError('员工ID不能为空');
    }

    if (!rejectReason) {
      throw new CustomError('拒绝原因不能为空');
    }

    // 验证追加服务是否属于该订单详情
    const additionalServiceOrder = await AdditionalServiceOrder.findOne({
      where: {
        id,
        orderDetailId,
      },
    });

    if (!additionalServiceOrder) {
      throw new CustomError('追加服务订单不存在');
    }

    return await this.service.rejectAdditionalService(
      id,
      employeeId,
      rejectReason
    );
  }

  @Post('/:id/pay', { summary: '支付追加服务订单' })
  async pay(
    @Param('orderDetailId') orderDetailId: number,
    @Param('id') id: number,
    @Body('customerId') customerId: number
  ) {
    if (!customerId) {
      throw new CustomError('客户ID不能为空');
    }

    // 验证追加服务是否属于该订单详情
    const additionalServiceOrder = await AdditionalServiceOrder.findOne({
      where: {
        id,
        orderDetailId,
      },
    });

    if (!additionalServiceOrder) {
      throw new CustomError('追加服务订单不存在');
    }

    return await this.service.payAdditionalServiceOrder(id, customerId);
  }

  @Del('/:id', { summary: '删除追加服务申请' })
  async delete(
    @Param('orderDetailId') orderDetailId: number,
    @Param('id') id: number,
    @Body('customerId') customerId?: number
  ) {
    // 验证追加服务是否属于该订单详情
    const additionalServiceOrder = await AdditionalServiceOrder.findOne({
      where: {
        id,
        orderDetailId,
      },
    });

    if (!additionalServiceOrder) {
      throw new CustomError('追加服务订单不存在');
    }

    return await this.service.deleteAdditionalServiceOrder(id, customerId);
  }

  @Get('/:id/status', { summary: '查询追加服务订单状态' })
  async getStatus(
    @Param('orderDetailId') orderDetailId: number,
    @Param('id') id: number
  ) {
    const order = await AdditionalServiceOrder.findOne({
      where: { id, orderDetailId },
      attributes: [
        'id',
        'sn',
        'status',
        'totalFee',
        'confirmTime',
        'payTime',
        'createdAt',
        'updatedAt',
      ],
    });

    console.log('【查询追加服务订单状态】：', order);

    return order;
  }

  @Post('/:id/sync-payment-status', { summary: '同步追加服务订单支付状态' })
  async syncPaymentStatus(
    @Param('orderDetailId') orderDetailId: number,
    @Param('id') id: number,
    @Body('operatorId') operatorId?: number
  ) {
    return await this.service.syncAdditionalServicePaymentStatus(
      id,
      orderDetailId,
      operatorId
    );
  }
}

// 员工端查询待确认的追加服务列表
@Controller('/employee/additional-services')
export class EmployeeAdditionalServiceController {
  @Get('/pending', { summary: '查询待确认的追加服务列表' })
  async getPendingList(
    @Query('employeeId') employeeId: number,
    @Query('current') current = 1,
    @Query('pageSize') pageSize = 10
  ) {
    if (!employeeId) {
      throw new CustomError('员工ID不能为空');
    }

    // 确保分页参数是数字类型
    const currentNum = Number(current);
    const pageSizeNum = Number(pageSize);
    const offset = (currentNum - 1) * pageSizeNum;

    const result = await AdditionalServiceOrder.findAndCountAll({
      where: {
        employeeId,
        status: AdditionalServiceOrderStatus.PENDING_CONFIRM,
      },
      include: [
        {
          model: AdditionalServiceOrderDetail,
          // 暂时移除错误的 Service 关联，等重启项目后使用正确的 AdditionalService 关联
        },
        {
          model: OrderDetail,
          include: [
            {
              model: Order,
              include: [Customer],
            },
          ],
        },
        {
          model: Customer,
          attributes: ['id', 'nickname', 'phone'],
        },
      ],
      order: [['updatedAt', 'DESC']],
      offset,
      limit: pageSizeNum,
    });

    return {
      list: result.rows,
      total: result.count,
      current: currentNum,
      pageSize: pageSizeNum,
    };
  }
}
