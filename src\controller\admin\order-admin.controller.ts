import {
  Controller,
  Get,
  Post,
  Del,
  Inject,
  Param,
  Query,
  Body,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { CustomError } from '../../error/custom.error';
import { OrderService } from '../../service/order.service';
import {
  Order,
  OrderDetail,
  AdditionalServiceOrder,
  AdditionalServiceOrderDetail,
  AdditionalServiceDiscountInfo,
  AdditionalService,
  Customer,
  Employee,
} from '../../entity';
import { AdditionalServiceOrderStatus } from '../../entity/additional-service-order.entity';

@Controller('/admin/orders')
export class OrderAdminController {
  @Inject()
  ctx: Context;

  @Inject()
  orderService: OrderService;

  @Get('/:orderId/additional-services', {
    summary: '管理端查询订单追加服务信息',
  })
  async getOrderAdditionalServices(@Param('orderId') orderId: number) {
    // 查询主订单信息
    const order = await Order.findByPk(orderId, {
      include: [
        {
          model: OrderDetail,
          include: [
            {
              model: AdditionalServiceOrder,
              include: [
                {
                  model: AdditionalServiceOrderDetail,
                  include: [
                    {
                      model: AdditionalService,
                      attributes: ['id', 'name', 'needDurationTracking'],
                    },
                  ],
                },
                {
                  model: AdditionalServiceDiscountInfo,
                },
              ],
            },
          ],
        },
        {
          model: Customer,
          attributes: ['id', 'nickname', 'phone'],
        },
        {
          model: Employee,
          attributes: ['id', 'name', 'phone'],
        },
      ],
    });

    if (!order) {
      throw new CustomError('订单不存在');
    }

    // 收集所有追加服务订单
    const allAdditionalServices = [];
    order.orderDetails?.forEach(detail => {
      if (detail.additionalServiceOrders) {
        allAdditionalServices.push(...detail.additionalServiceOrders);
      }
    });

    // 计算追加服务汇总信息
    const summary = {
      totalCount: allAdditionalServices.length,
      totalAmount: allAdditionalServices.reduce(
        (sum, service) => sum + Number(service.totalFee),
        0
      ),
      totalOriginalPrice: allAdditionalServices.reduce(
        (sum, service) => sum + Number(service.originalPrice),
        0
      ),
      totalDeduction: allAdditionalServices.reduce(
        (sum, service) =>
          sum + Number(service.cardDeduction) + Number(service.couponDeduction),
        0
      ),
      paidCount: allAdditionalServices.filter(
        service => service.status === AdditionalServiceOrderStatus.PAID
      ).length,
      paidAmount: allAdditionalServices
        .filter(service => service.status === AdditionalServiceOrderStatus.PAID)
        .reduce((sum, service) => sum + Number(service.totalFee), 0),
      pendingCount: allAdditionalServices.filter(service =>
        [
          AdditionalServiceOrderStatus.PENDING_CONFIRM,
          AdditionalServiceOrderStatus.CONFIRMED,
        ].includes(service.status)
      ).length,
      pendingAmount: allAdditionalServices
        .filter(service =>
          [
            AdditionalServiceOrderStatus.PENDING_CONFIRM,
            AdditionalServiceOrderStatus.CONFIRMED,
          ].includes(service.status)
        )
        .reduce((sum, service) => sum + Number(service.totalFee), 0),
    };

    // 格式化追加服务列表
    const additionalServices = allAdditionalServices.map(service => ({
      id: service.id,
      sn: service.sn,
      status: service.status,
      originalPrice: service.originalPrice,
      totalFee: service.totalFee,
      cardDeduction: service.cardDeduction,
      couponDeduction: service.couponDeduction,
      payTime: service.payTime,
      confirmTime: service.confirmTime,
      rejectReason: service.rejectReason,
      details:
        service.details?.map(detail => ({
          serviceName: detail.serviceName,
          servicePrice: detail.servicePrice,
          quantity: detail.quantity,
        })) || [],
      discountInfos:
        service.discountInfos?.map(discount => ({
          discountType: discount.discountType,
          discountAmount: discount.discountAmount,
        })) || [],
      createdAt: service.createdAt,
    }));

    return {
      orderId: order.id,
      orderSn: order.sn,
      additionalServiceSummary: summary,
      additionalServices: additionalServices.sort(
        (a, b) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      ),
    };
  }

  @Get('/', { summary: '管理端订单列表（包含追加服务信息）' })
  async getOrdersWithAdditionalServices(
    @Query('current') current = 1,
    @Query('pageSize') pageSize = 20,
    @Query('includeAdditionalServices') includeAdditionalServices = true,
    @Query() otherParams: any
  ) {
    // 这里可以复用现有的订单查询逻辑，并在结果中添加追加服务摘要信息
    // 为了简化，这里只提供基本的查询逻辑

    const includeOptions = [
      {
        model: Customer,
        attributes: ['id', 'nickname', 'phone'],
      },
      {
        model: Employee,
        attributes: ['id', 'name', 'phone'],
      },
    ];

    // 如果需要包含追加服务信息，添加相关的include
    if (includeAdditionalServices) {
      includeOptions.push({
        model: OrderDetail,
        include: [
          {
            model: AdditionalServiceOrder,
            attributes: [
              'id',
              'status',
              'totalFee',
              'originalPrice',
              'createdAt',
            ],
          },
        ],
      } as any);
    }

    const { count, rows } = await Order.findAndCountAll({
      include: includeOptions,
      order: [['updatedAt', 'DESC']],
      offset: (current - 1) * pageSize,
      limit: pageSize,
    });

    // 格式化结果，添加追加服务摘要信息
    const list = rows.map(order => {
      const baseOrder = {
        id: order.id,
        sn: order.sn,
        status: order.status,
        totalFee: order.totalFee,
        customer: order.customer,
        employee: order.employee,
        createdAt: order.createdAt,
      };

      if (includeAdditionalServices && order.orderDetails) {
        // 收集所有追加服务订单
        const allAdditionalServices = [];
        order.orderDetails.forEach(detail => {
          if (detail.additionalServiceOrders) {
            allAdditionalServices.push(...detail.additionalServiceOrders);
          }
        });

        const additionalServiceSummary = {
          hasAdditionalServices: allAdditionalServices.length > 0,
          totalCount: allAdditionalServices.length,
          totalAmount: allAdditionalServices.reduce(
            (sum, service) => sum + Number(service.totalFee),
            0
          ),
          paidCount: allAdditionalServices.filter(
            service => service.status === AdditionalServiceOrderStatus.PAID
          ).length,
          paidAmount: allAdditionalServices
            .filter(
              service => service.status === AdditionalServiceOrderStatus.PAID
            )
            .reduce((sum, service) => sum + Number(service.totalFee), 0),
          pendingCount: allAdditionalServices.filter(service =>
            [
              AdditionalServiceOrderStatus.PENDING_CONFIRM,
              AdditionalServiceOrderStatus.CONFIRMED,
            ].includes(service.status)
          ).length,
          pendingAmount: allAdditionalServices
            .filter(service =>
              [
                AdditionalServiceOrderStatus.PENDING_CONFIRM,
                AdditionalServiceOrderStatus.CONFIRMED,
              ].includes(service.status)
            )
            .reduce((sum, service) => sum + Number(service.totalFee), 0),
          lastAdditionalServiceTime:
            allAdditionalServices.length > 0
              ? Math.max(
                  ...allAdditionalServices.map(service =>
                    new Date(service.createdAt).getTime()
                  )
                )
              : null,
        };

        return {
          ...baseOrder,
          additionalServiceSummary,
        };
      }

      return baseOrder;
    });

    return {
      list,
      total: count,
      current,
      pageSize,
    };
  }

  @Post('/:orderId/admin-cancel', { summary: '管理员取消订单（任何状态）' })
  async adminCancelOrder(
    @Param('orderId') orderId: number,
    @Body() body: { operatorId: number; reason?: string }
  ) {
    if (!body.operatorId) {
      throw new CustomError('操作员ID不能为空', 400);
    }

    const result = await this.orderService.adminCancelOrder(
      orderId,
      body.operatorId,
      body.reason
    );
    return result;
  }

  @Post('/sn/:sn/admin-apply-refund', { summary: '管理员申请退款（任何状态）' })
  async adminApplyRefund(
    @Param('sn') sn: string,
    @Body() body: { operatorId: number; reason?: string }
  ) {
    if (!body.operatorId) {
      throw new CustomError('操作员ID不能为空', 400);
    }

    const result = await this.orderService.adminApplyRefund(
      sn,
      body.operatorId,
      body.reason
    );
    return result;
  }

  @Del('/:orderId/admin-delete', { summary: '管理员删除订单（任何状态）' })
  async adminDeleteOrder(
    @Param('orderId') orderId: number,
    @Body() body: { operatorId: number; reason?: string }
  ) {
    if (!body.operatorId) {
      throw new CustomError('操作员ID不能为空', 400);
    }

    const result = await this.orderService.adminDeleteOrder(
      orderId,
      body.operatorId,
      body.reason
    );
    return result;
  }
}
