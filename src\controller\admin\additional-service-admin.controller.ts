import {
  Controller,
  Get,
  Post,
  Inject,
  Param,
  Query,
  Body,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { AdditionalServiceOrderService } from '../../service/additional-service-order.service';
import { WepayService } from '../../service/wepay.service';
import { CustomError } from '../../error/custom.error';
import {
  AdditionalServiceOrder,
  AdditionalServiceOrderDetail,
  OrderDetail,
  Order,
  Customer,
  Employee,
} from '../../entity';
import { Op } from 'sequelize';

@Controller('/admin/additional-services')
export class AdditionalServiceAdminController {
  @Inject()
  ctx: Context;

  @Inject()
  additionalServiceOrderService: AdditionalServiceOrderService;

  @Inject()
  wepayService: WepayService;

  @Get('/', { summary: '管理端查询追加服务订单列表' })
  async list(
    @Query('current') current = 1,
    @Query('pageSize') pageSize = 20,
    @Query('status') status?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('customerPhone') customerPhone?: string,
    @Query('employeeId') employeeId?: number,
    @Query('orderSn') orderSn?: string,
    @Query('additionalServiceSn') additionalServiceSn?: string
  ) {
    const where: any = {};
    const customerWhere: any = {};
    const orderWhere: any = {};

    // 状态筛选
    if (status) {
      where.status = status.split(',');
    }

    // 日期筛选
    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) {
        where.createdAt[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        where.createdAt[Op.lte] = new Date(endDate + ' 23:59:59');
      }
    }

    // 客户手机号筛选
    if (customerPhone) {
      customerWhere.phone = {
        [Op.like]: `%${customerPhone}%`,
      };
    }

    // 员工ID筛选
    if (employeeId) {
      where.employeeId = employeeId;
    }

    // 主订单号筛选
    if (orderSn) {
      orderWhere.sn = {
        [Op.like]: `%${orderSn}%`,
      };
    }

    // 追加服务订单号筛选
    if (additionalServiceSn) {
      where.sn = {
        [Op.like]: `%${additionalServiceSn}%`,
      };
    }

    const { count, rows } = await AdditionalServiceOrder.findAndCountAll({
      where,
      include: [
        {
          model: AdditionalServiceOrderDetail,
          // 暂时移除错误的 Service 关联，等重启项目后使用正确的 AdditionalService 关联
        },
        {
          model: OrderDetail,
          include: [
            {
              model: Order,
              where:
                Object.keys(orderWhere).length > 0 ? orderWhere : undefined,
              include: [
                {
                  model: Customer,
                  where:
                    Object.keys(customerWhere).length > 0
                      ? customerWhere
                      : undefined,
                  attributes: ['id', 'nickname', 'phone'],
                },
                {
                  model: Employee,
                  attributes: ['id', 'name', 'phone'],
                },
              ],
            },
          ],
        },
        {
          model: Customer,
          attributes: ['id', 'nickname', 'phone'],
        },
        {
          model: Employee,
          attributes: ['id', 'name', 'phone'],
        },
      ],
      order: [['createdAt', 'DESC']],
      offset: (current - 1) * pageSize,
      limit: pageSize,
    });

    // 计算汇总信息
    const summary = {
      totalCount: count,
      totalAmount: rows.reduce((sum, order) => sum + Number(order.totalFee), 0),
      totalOriginalPrice: rows.reduce(
        (sum, order) => sum + Number(order.originalPrice),
        0
      ),
      totalDeduction: rows.reduce(
        (sum, order) =>
          sum + Number(order.cardDeduction) + Number(order.couponDeduction),
        0
      ),
    };

    return {
      list: rows.map(order => ({
        id: order.id,
        sn: order.sn,
        status: order.status,
        originalPrice: order.originalPrice,
        totalFee: order.totalFee,
        cardDeduction: order.cardDeduction,
        couponDeduction: order.couponDeduction,
        payTime: order.payTime,
        confirmTime: order.confirmTime,
        mainOrder: {
          id: order.orderDetail.order.id,
          sn: order.orderDetail.order.sn,
          status: order.orderDetail.order.status,
          customer: order.orderDetail.order.customer,
          employee: order.orderDetail.order.employee,
        },
        details:
          order.details?.map(detail => ({
            serviceName: detail.serviceName,
            servicePrice: detail.servicePrice,
            quantity: detail.quantity,
          })) || [],
        createdAt: order.createdAt,
      })),
      total: count,
      current,
      pageSize,
      summary,
    };
  }

  @Get('/statistics', { summary: '管理端查询追加服务数据统计' })
  async statistics(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('groupBy') groupBy?: 'day' | 'month'
  ) {
    const where: any = {};

    // 日期筛选
    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) {
        where.createdAt[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        where.createdAt[Op.lte] = new Date(endDate + ' 23:59:59');
      }
    }

    // 基础统计
    const totalOrders = await AdditionalServiceOrder.count({ where });
    const paidOrders = await AdditionalServiceOrder.findAll({
      where: { ...where, status: 'paid' },
      attributes: [
        'totalFee',
        'originalPrice',
        'cardDeduction',
        'couponDeduction',
      ],
    });

    const totalAmount = paidOrders.reduce(
      (sum, order) => sum + Number(order.totalFee),
      0
    );
    const totalOriginalPrice = paidOrders.reduce(
      (sum, order) => sum + Number(order.originalPrice),
      0
    );
    const totalDeduction = paidOrders.reduce(
      (sum, order) =>
        sum + Number(order.cardDeduction) + Number(order.couponDeduction),
      0
    );

    // 状态分布
    const statusDistribution = await AdditionalServiceOrder.findAll({
      where,
      attributes: [
        'status',
        [AdditionalServiceOrder.sequelize.fn('COUNT', '*'), 'count'],
      ],
      group: ['status'],
      raw: true,
    });

    const statusMap = statusDistribution.reduce((acc, item: any) => {
      acc[item.status] = parseInt(item.count);
      return acc;
    }, {});

    return {
      overview: {
        totalOrders,
        totalAmount,
        totalOriginalPrice,
        totalDeduction,
        averageOrderAmount: totalOrders > 0 ? totalAmount / totalOrders : 0,
        statusDistribution: statusMap,
      },
      // 这里可以根据需要添加更多统计维度
      trends: [], // 趋势数据
      topServices: [], // 热门服务
      employeeRanking: [], // 员工排名
    };
  }

  @Post('/:id/sync-payment-status', {
    summary: '管理端手动同步追加服务支付状态',
  })
  async syncPaymentStatus(
    @Param('id') id: number,
    @Body('operatorId') operatorId: number,
    @Body('reason') reason?: string
  ) {
    if (!operatorId) {
      throw new CustomError('操作员ID不能为空');
    }

    const order = await AdditionalServiceOrder.findByPk(id);
    if (!order) {
      throw new CustomError('追加服务订单不存在');
    }

    const result =
      await this.additionalServiceOrderService.syncAdditionalServicePaymentStatus(
        id,
        order.orderDetailId,
        operatorId
      );

    // 记录操作日志
    this.ctx.logger.info('【管理端同步支付状态】：', {
      orderId: id,
      orderSn: order.sn,
      operatorId,
      reason,
      result,
    });

    return {
      ...result,
      operator: {
        id: operatorId,
      },
    };
  }

  @Post('/batch-operation', { summary: '管理端批量操作追加服务订单' })
  async batchOperation(
    @Body('operation') operation: string,
    @Body('orderIds') orderIds: number[],
    @Body('operatorId') operatorId: number,
    @Body('reason') reason?: string
  ) {
    if (!operation) {
      throw new CustomError('操作类型不能为空');
    }

    if (!orderIds || orderIds.length === 0) {
      throw new CustomError('订单ID列表不能为空');
    }

    if (!operatorId) {
      throw new CustomError('操作员ID不能为空');
    }

    const results = [];
    let successCount = 0;
    let failedCount = 0;

    for (const orderId of orderIds) {
      try {
        if (operation === 'sync_payment_status') {
          const order = await AdditionalServiceOrder.findByPk(orderId);
          if (!order) {
            results.push({
              orderId,
              success: false,
              message: '订单不存在',
            });
            failedCount++;
            continue;
          }

          const result =
            await this.additionalServiceOrderService.syncAdditionalServicePaymentStatus(
              orderId,
              order.orderDetailId,
              operatorId
            );

          results.push({
            orderId,
            success: result.success,
            message: result.message,
          });

          if (result.success) {
            successCount++;
          } else {
            failedCount++;
          }
        } else {
          results.push({
            orderId,
            success: false,
            message: '不支持的操作类型',
          });
          failedCount++;
        }
      } catch (error) {
        results.push({
          orderId,
          success: false,
          message: error.message,
        });
        failedCount++;
      }
    }

    // 记录批量操作日志
    this.ctx.logger.info('【管理端批量操作】：', {
      operation,
      orderIds,
      operatorId,
      reason,
      totalCount: orderIds.length,
      successCount,
      failedCount,
    });

    return {
      totalCount: orderIds.length,
      successCount,
      failedCount,
      results,
    };
  }

  @Post('/:id/refund', { summary: '管理端处理追加服务退款' })
  async refundAdditionalService(
    @Param('id') id: number,
    @Body('operatorId') operatorId: number,
    @Body('reason') reason: string,
    @Body('refundAmount') refundAmount?: number,
    @Body('shouldRefundCoupons') shouldRefundCoupons = true
  ) {
    if (!operatorId) {
      throw new CustomError('操作员ID不能为空');
    }

    if (!reason) {
      throw new CustomError('退款原因不能为空');
    }

    const order = await AdditionalServiceOrder.findByPk(id);
    if (!order) {
      throw new CustomError('追加服务订单不存在');
    }

    // 调用管理员专用退款方法（无状态检查）
    const result =
      await this.additionalServiceOrderService.adminRefundAdditionalServiceOrder(
        id,
        reason,
        shouldRefundCoupons
      );

    // 记录操作日志
    this.ctx.logger.info('【管理端追加服务退款】：', {
      orderId: id,
      orderSn: order.sn,
      operatorId,
      reason,
      refundAmount: refundAmount || order.totalFee,
      shouldRefundCoupons,
      result,
    });

    return {
      success: true,
      refundAmount: refundAmount || order.totalFee,
      refundStatus: 'completed',
      message: '追加服务退款成功',
      operator: {
        id: operatorId,
      },
    };
  }
}
