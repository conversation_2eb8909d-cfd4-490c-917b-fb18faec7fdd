-- 修复追加服务订单明细表的外键关联
-- 将 serviceId 的外键从 services 表改为 additional_services 表

-- 1. 删除原有的外键约束
ALTER TABLE additional_service_order_details 
DROP FOREIGN KEY additional_service_order_details_ibfk_2;

-- 2. 添加新的外键约束到 additional_services 表
ALTER TABLE additional_service_order_details 
ADD CONSTRAINT fk_additional_service_order_details_service_id 
FOREIGN KEY (serviceId) REFERENCES additional_services(id) 
ON DELETE NO ACTION ON UPDATE CASCADE;

-- 3. 更新注释
ALTER TABLE additional_service_order_details 
MODIFY COLUMN serviceId INT NOT NULL COMMENT '关联的增项服务ID';

-- 4. 更新服务名称字段注释
ALTER TABLE additional_service_order_details 
MODIFY COLUMN serviceName VARCHAR(100) NOT NULL COMMENT '服务名称（冗余字段，确保删除增项服务后明细的服务名称不丢失）';

-- 5. 更新服务价格字段注释
ALTER TABLE additional_service_order_details 
MODIFY COLUMN servicePrice DECIMAL(8,2) NOT NULL COMMENT '服务价格（冗余字段，确保增项服务价格变更后明细的价格不受影响）';
