import {
  Table,
  Column,
  Model,
  DataType,
  BelongsTo,
  ForeignKey,
} from 'sequelize-typescript';
import { AdditionalServiceOrder, Service } from './';

/**
 * 追加服务订单明细属性接口
 */
export interface AdditionalServiceOrderDetailAttributes {
  /** 明细ID */
  id: number;
  /** 关联的追加服务订单ID */
  additionalServiceOrderId: number;
  /** 关联的服务ID */
  serviceId: number;
  /** 服务名称（冗余字段，确保删除服务后明细的服务名称不丢失） */
  serviceName: string;
  /** 服务价格（冗余字段，确保服务价格变更后明细的价格不受影响） */
  servicePrice: number;
  /** 数量 */
  quantity: number;
  /** 创建时间 */
  createdAt?: Date;
  /** 更新时间 */
  updatedAt?: Date;
  /** 关联的追加服务订单 */
  additionalServiceOrder?: AdditionalServiceOrder;
  /** 关联的服务 */
  service?: Service;
}

/**
 * 追加服务订单明细表
 */
@Table({
  tableName: 'additional_service_order_details',
  timestamps: true,
  comment: '追加服务订单明细表',
})
export class AdditionalServiceOrderDetail
  extends Model<AdditionalServiceOrderDetailAttributes>
  implements AdditionalServiceOrderDetailAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '明细ID',
  })
  id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '关联的追加服务订单ID',
  })
  @ForeignKey(() => AdditionalServiceOrder)
  additionalServiceOrderId: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment:
      '关联的服务ID（注意：实际存储的是AdditionalService的ID，但外键关联到Service表）',
  })
  @ForeignKey(() => Service)
  serviceId: number;

  @Column({
    type: DataType.STRING(100),
    allowNull: false,
    comment: '服务名称（冗余字段，确保删除服务后明细的服务名称不丢失）',
  })
  serviceName: string;

  @Column({
    type: DataType.DECIMAL(8, 2),
    allowNull: false,
    comment: '服务价格（冗余字段，确保服务价格变更后明细的价格不受影响）',
  })
  servicePrice: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 1,
    comment: '数量',
  })
  quantity: number;

  @BelongsTo(() => AdditionalServiceOrder, { onDelete: 'CASCADE' })
  additionalServiceOrder?: AdditionalServiceOrder;

  @BelongsTo(() => Service, { onDelete: 'NO ACTION' })
  service?: Service;
}
